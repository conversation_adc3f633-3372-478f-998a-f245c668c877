// Core utility exports for game development
import { MathUtils } from "./MathUtils";
import { StringUtils } from "./StringUtils";
import { TimeUtils } from "./TimeUtils";
import { TableUtils } from "./TableUtils";
import { PositionHelper } from "./PositionHelper";

export { MathUtils, StringUtils, TimeUtils, TableUtils, PositionHelper };

/**
 * Convenience re-exports for common operations
 */
export const Utils = {
	Math: MathUtils,
	String: StringUtils,
	Time: TimeUtils,
	Table: TableUtils,
	Position: PositionHelper,
};

/**
 * Quick utility functions for common tasks
 */
export const QuickUtils = {
	/**
	 * Format any number for display
	 */
	formatNumber: (num: number) => StringUtils.formatNumber(num),

	/**
	 * Format time duration
	 */
	formatTime: (seconds: number) => StringUtils.formatTime(seconds),

	/**
	 * Random between min and max
	 */
	random: (min: number, max: number) => math.random() * (max - min) + min,

	/**
	 * Random integer between min and max (inclusive)
	 */
	randomInt: (min: number, max: number) => math.floor(math.random() * (max - min + 1)) + min,

	/**
	 * Clamp value between min and max
	 */
	clamp: (value: number, min: number, max: number) => MathUtils.clamp(value, min, max),

	/**
	 * Linear interpolation
	 */
	lerp: (a: number, b: number, t: number) => MathUtils.lerp(a, b, t),

	/**
	 * Check if point is in range of another point
	 */
	inRange: (pos1: Vector3, pos2: Vector3, range: number) => MathUtils.distance(pos1, pos2) <= range,

	/**
	 * Get distance between two points
	 */
	distance: (pos1: Vector3, pos2: Vector3) => MathUtils.distance(pos1, pos2),

	/**
	 * Round number to decimal places
	 */
	round: (value: number, decimals = 0) => MathUtils.round(value, decimals),

	/**
	 * Deep copy any object
	 */
	copy: <T>(obj: T) => TableUtils.deepCopy(obj),

	/**
	 * Wait for a condition
	 */
	waitFor: (condition: () => boolean, timeout = 10) => TimeUtils.waitFor(condition, timeout),

	/**
	 * Generate a unique ID
	 */
	generateId: () => StringUtils.generateId(),

	/**
	 * Check if string is empty or whitespace
	 */
	isEmpty: (str: string) => str.gsub("%s", "")[0] === "",

	/**
	 * Capitalize first letter
	 */
	capitalize: (str: string) => str.sub(1, 1).upper() + str.sub(2).lower(),

	/**
	 * Get current game time
	 */
	gameTime: () => TimeUtils.getGameTime(),

	/**
	 * Format player name for display
	 */
	playerName: (player: Player) => StringUtils.formatPlayerName(player),

	/**
	 * Safe color conversion that handles transparent and rgba values
	 */
	safeColor: (colorValue: string, fallback = "#FFFFFF") => {
		try {
			// Handle transparent case
			if (colorValue === "transparent") {
				return Color3.fromHex(fallback);
			}

			// Handle rgba values - extract color part if present
			if (colorValue.find("rgba%(")[0] !== undefined) {
				// For rgba, we'll just use the fallback color
				// since Color3.fromHex can't handle alpha channels
				return Color3.fromHex(fallback);
			}

			// Handle normal hex values
			return Color3.fromHex(colorValue);
		} catch {
			return Color3.fromHex(fallback);
		}
	},

	/**
	 * Check if value exists and is not nil
	 */
	exists: (value: unknown) => value !== undefined,

	/**
	 * Get value or default
	 */
	default: <T>(value: T | undefined, defaultValue: T): T => {
		return value !== undefined ? value : defaultValue;
	},
};

print("🔧 Core utilities loaded: Math, String, Time, Table, Position helpers");
print("💡 Access via Utils.Math, Utils.String, etc. or QuickUtils for common functions");
