import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { BaseFrameProps } from "./types/BaseFrameProps";

export function Frame(props: BaseFrameProps): React.ReactElement {
	const backgroundColor = props.backgroundColor ?? COLORS.bg.base;
	const backgroundTransparency = props.backgroundTransparency ?? 0;
	const padding = props.padding ?? 0;

	return (
		<frame
			BackgroundColor3={Color3.fromHex(backgroundColor)}
			BackgroundTransparency={backgroundTransparency}
			Size={props.size ?? new UDim2(1, 0, 1, 0)}
			Position={props.position}
			AnchorPoint={props.anchorPoint}
			LayoutOrder={props.layoutOrder}
			BorderSizePixel={0}
			ZIndex={props.zIndex}
			AutomaticSize={props.autoSize ?? Enum.AutomaticSize.None}
			ClipsDescendants={true}
		>
			{/* Enhanced padding system */}
			{padding > 0 ? (
				<uipadding
					PaddingTop={new UDim(0, padding)}
					PaddingBottom={new UDim(0, padding)}
					PaddingLeft={new UDim(0, padding)}
					PaddingRight={new UDim(0, padding)}
				/>
			) : undefined}

			{props.children}
		</frame>
	);
}
