-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
local _types = TS.import(script, game:GetService("ReplicatedStorage"), "core", "quests", "types")
local QuestStatus = _types.QuestStatus
local ObjectiveType = _types.ObjectiveType
local QuestType = _types.QuestType
--[[
	*
	 * Individual quest instance with progress tracking
	 
]]
local Quest
do
	Quest = setmetatable({}, {
		__tostring = function()
			return "Quest"
		end,
	})
	Quest.__index = Quest
	function Quest.new(...)
		local self = setmetatable({}, Quest)
		return self:constructor(...) or self
	end
	function Quest:constructor(definition)
		self.eventCallbacks = {}
		local _object = table.clone(definition)
		setmetatable(_object, nil)
		self.definition = _object
		self.progress = {
			questId = definition.id,
			status = QuestStatus.NotStarted,
			objectives = {},
			timesCompleted = 0,
		}
		-- Initialize objectives
		local _exp = self.definition.objectives
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(obj)
			local _objectives = self.progress.objectives
			local _exp_1 = obj.id
			local _object_1 = table.clone(obj)
			setmetatable(_object_1, nil)
			_objectives[_exp_1] = _object_1
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function Quest:start()
		if self.progress.status ~= QuestStatus.NotStarted and self.progress.status ~= QuestStatus.Available then
			return Result:err("Quest is already started or completed")
		end
		self.progress.status = QuestStatus.Active
		self.progress.startTime = tick()
		-- Set expiration time if applicable
		local _value = self.definition.expirationHours
		if _value ~= 0 and _value == _value and _value then
			self.progress.expirationTime = tick() + self.definition.expirationHours * 3600
		end
		self:fireEvent({
			type = "started",
			questId = self.definition.id,
			timestamp = tick(),
		})
		return Result:ok(true)
	end
	function Quest:complete(force)
		if force == nil then
			force = false
		end
		if self.progress.status ~= QuestStatus.Active and not force then
			return Result:err("Quest is not active")
		end
		if not self:areRequiredObjectivesCompleted() and not force then
			return Result:err("Not all required objectives are completed")
		end
		self.progress.status = QuestStatus.Completed
		self.progress.completionTime = tick()
		self.progress.timesCompleted += 1
		self.progress.lastCompletionTime = tick()
		self:fireEvent({
			type = "completed",
			questId = self.definition.id,
			timestamp = tick(),
		})
		return Result:ok(true)
	end
	function Quest:fail(reason)
		if self.progress.status ~= QuestStatus.Active then
			return Result:err("Quest is not active")
		end
		self.progress.status = QuestStatus.Failed
		self:fireEvent({
			type = "failed",
			questId = self.definition.id,
			timestamp = tick(),
		})
		return Result:ok(true)
	end
	function Quest:abandon()
		if self.progress.status ~= QuestStatus.Active then
			return Result:err("Quest is not active")
		end
		self.progress.status = QuestStatus.Abandoned
		self:fireEvent({
			type = "abandoned",
			questId = self.definition.id,
			timestamp = tick(),
		})
		return Result:ok(true)
	end
	function Quest:updateObjective(objectiveId, increment)
		if increment == nil then
			increment = 1
		end
		if self.progress.status ~= QuestStatus.Active then
			return Result:err("Quest is not active")
		end
		local _objectives = self.progress.objectives
		local _objectiveId = objectiveId
		local objective = _objectives[_objectiveId]
		if not objective then
			return Result:err(`Objective {objectiveId} not found`)
		end
		if objective.isCompleted then
			return Result:ok(false)
		end
		-- Check prerequisites
		local _result = objective.requirements
		if _result ~= nil then
			_result = _result.prerequisites
		end
		if _result then
			local _exp = objective.requirements.prerequisites
			-- ▼ ReadonlyArray.every ▼
			local _result_1 = true
			local _callback = function(prereqId)
				local _objectives_1 = self.progress.objectives
				local _prereqId = prereqId
				local prereq = _objectives_1[_prereqId]
				local _result_2 = prereq
				if _result_2 ~= nil then
					_result_2 = _result_2.isCompleted
				end
				local _condition = _result_2
				if not _condition then
					_condition = false
				end
				return _condition
			end
			for _k, _v in _exp do
				if not _callback(_v, _k - 1, _exp) then
					_result_1 = false
					break
				end
			end
			-- ▲ ReadonlyArray.every ▲
			local prerequisitesMet = _result_1
			if not prerequisitesMet then
				return Result:err("Objective prerequisites not met")
			end
		end
		-- Update count
		objective.currentCount = math.min(objective.currentCount + increment, objective.targetCount)
		-- Check if completed
		if objective.currentCount >= objective.targetCount then
			objective.isCompleted = true
			self:fireEvent({
				type = "objective_completed",
				questId = self.definition.id,
				objectiveId = objectiveId,
				timestamp = tick(),
			})
			-- Auto-complete quest if enabled and all objectives are done
			if self.definition.autoComplete and self:areRequiredObjectivesCompleted() then
				self:complete()
			end
		end
		return Result:ok(true)
	end
	function Quest:setObjectiveProgress(objectiveId, count)
		if self.progress.status ~= QuestStatus.Active then
			return Result:err("Quest is not active")
		end
		local _objectives = self.progress.objectives
		local _objectiveId = objectiveId
		local objective = _objectives[_objectiveId]
		if not objective then
			return Result:err(`Objective {objectiveId} not found`)
		end
		local previousCount = objective.currentCount
		objective.currentCount = math.min(math.max(count, 0), objective.targetCount)
		-- Check if completed
		if objective.currentCount >= objective.targetCount and not objective.isCompleted then
			objective.isCompleted = true
			self:fireEvent({
				type = "objective_completed",
				questId = self.definition.id,
				objectiveId = objectiveId,
				timestamp = tick(),
			})
			-- Auto-complete quest if enabled and all objectives are done
			if self.definition.autoComplete and self:areRequiredObjectivesCompleted() then
				self:complete()
			end
		end
		return Result:ok(objective.currentCount ~= previousCount)
	end
	function Quest:checkExpiration()
		local _condition = self.progress.expirationTime
		if _condition ~= 0 and _condition == _condition and _condition then
			_condition = tick() > self.progress.expirationTime
		end
		if _condition ~= 0 and _condition == _condition and _condition then
			if self.progress.status == QuestStatus.Active then
				self.progress.status = QuestStatus.Expired
				self:fireEvent({
					type = "expired",
					questId = self.definition.id,
					timestamp = tick(),
				})
			end
			return true
		end
		return false
	end
	function Quest:canRepeat()
		if not self.definition.isRepeatable then
			return false
		end
		if self.progress.status ~= QuestStatus.Completed then
			return false
		end
		local _condition = self.definition.cooldownHours
		if _condition ~= 0 and _condition == _condition and _condition then
			_condition = self.progress.lastCompletionTime
		end
		if _condition ~= 0 and _condition == _condition and _condition then
			local cooldownEnd = self.progress.lastCompletionTime + self.definition.cooldownHours * 3600
			return tick() >= cooldownEnd
		end
		return true
	end
	function Quest:reset()
		if not self:canRepeat() then
			return Result:err("Quest cannot be repeated yet")
		end
		self.progress.status = QuestStatus.Available
		self.progress.startTime = nil
		self.progress.completionTime = nil
		self.progress.expirationTime = nil
		-- Reset objectives
		local _exp = self.definition.objectives
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(obj)
			local _object = table.clone(obj)
			setmetatable(_object, nil)
			local resetObj = _object
			resetObj.currentCount = 0
			resetObj.isCompleted = false
			local _objectives = self.progress.objectives
			local _id = obj.id
			_objectives[_id] = resetObj
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		return Result:ok(true)
	end
	function Quest:getDefinition()
		local _object = table.clone(self.definition)
		setmetatable(_object, nil)
		return _object
	end
	function Quest:getProgress()
		local _object = table.clone(self.progress)
		setmetatable(_object, nil)
		_object.objectives = self:cloneObjectivesMap(self.progress.objectives)
		return _object
	end
	function Quest:cloneObjectivesMap(originalMap)
		local newMap = {}
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			local _exp = key
			local _object = table.clone(value)
			setmetatable(_object, nil)
			newMap[_exp] = _object
		end
		for _k, _v in originalMap do
			_callback(_v, _k, originalMap)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return newMap
	end
	function Quest:objectivesMapToArray(objectivesMap)
		local objectives = {}
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(objective)
			local _objective = objective
			table.insert(objectives, _objective)
			return #objectives
		end
		for _k, _v in objectivesMap do
			_callback(_v, _k, objectivesMap)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return objectives
	end
	function Quest:getCompletionPercentage()
		local objectives = self:objectivesMapToArray(self.progress.objectives)
		if #objectives == 0 then
			return 0
		end
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback = function(sum, obj)
			return sum + obj.currentCount / obj.targetCount
		end
		for _i = 1, #objectives do
			_result = _callback(_result, objectives[_i], _i - 1, objectives)
		end
		-- ▲ ReadonlyArray.reduce ▲
		local totalProgress = _result
		return (totalProgress / #objectives) * 100
	end
	function Quest:getVisibleObjectives()
		local _exp = self:objectivesMapToArray(self.progress.objectives)
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(obj)
			return not obj.isHidden
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Quest:getCompletedObjectives()
		local _exp = self:objectivesMapToArray(self.progress.objectives)
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(obj)
			return obj.isCompleted
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Quest:getRemainingTime()
		local _value = self.progress.expirationTime
		if not (_value ~= 0 and _value == _value and _value) then
			return nil
		end
		return math.max(0, self.progress.expirationTime - tick())
	end
	function Quest:onEvent(callback)
		local _eventCallbacks = self.eventCallbacks
		local _callback = callback
		table.insert(_eventCallbacks, _callback)
		return function()
			local _eventCallbacks_1 = self.eventCallbacks
			local _callback_1 = callback
			local index = (table.find(_eventCallbacks_1, _callback_1) or 0) - 1
			if index >= 0 then
				table.remove(self.eventCallbacks, index + 1)
			end
		end
	end
	function Quest:areRequiredObjectivesCompleted()
		local _exp = self:objectivesMapToArray(self.progress.objectives)
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(obj)
			return not obj.isOptional
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local requiredObjectives = _newValue
		-- ▼ ReadonlyArray.every ▼
		local _result = true
		local _callback_1 = function(obj)
			return obj.isCompleted
		end
		for _k, _v in requiredObjectives do
			if not _callback_1(_v, _k - 1, requiredObjectives) then
				_result = false
				break
			end
		end
		-- ▲ ReadonlyArray.every ▲
		return _result
	end
	function Quest:fireEvent(event)
		local _exp = self.eventCallbacks
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(callback)
			TS.try(function()
				callback(event)
			end, function(error)
				warn(`Quest event callback failed: {error}`)
			end)
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
end
--[[
	*
	 * Quest Manager service for managing all quests
	 
]]
local QuestManager
do
	local super = BaseService
	QuestManager = setmetatable({}, {
		__tostring = function()
			return "QuestManager"
		end,
		__index = super,
	})
	QuestManager.__index = QuestManager
	function QuestManager.new(...)
		local self = setmetatable({}, QuestManager)
		return self:constructor(...) or self
	end
	function QuestManager:constructor()
		super.constructor(self, "QuestManager")
		self.questDefinitions = {}
		self.activeQuests = {}
		self.completedQuests = {}
		self.eventCallbacks = {}
		self.config = {
			enableAutoProgress = true,
			enableNotifications = true,
			maxActiveQuests = 20,
			enableQuestLog = true,
			enableQuestTracking = true,
			saveProgress = true,
		}
	end
	function QuestManager:getInstance()
		if not QuestManager.instance then
			QuestManager.instance = QuestManager.new()
		end
		return QuestManager.instance
	end
	QuestManager.onInitialize = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			self:loadDefaultQuests()
			print("📜 Quest Manager initialized")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize QuestManager: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	QuestManager.onShutdown = TS.async(function(self)
		table.clear(self.questDefinitions)
		table.clear(self.activeQuests)
		table.clear(self.completedQuests)
		self.eventCallbacks = {}
		print("📜 Quest Manager shutdown")
		return Result:ok(nil)
	end)
	function QuestManager:registerQuest(definition)
		local _questDefinitions = self.questDefinitions
		local _id = definition.id
		if _questDefinitions[_id] ~= nil then
			return Result:err(`Quest '{definition.id}' already registered`)
		end
		local _questDefinitions_1 = self.questDefinitions
		local _exp = definition.id
		local _object = table.clone(definition)
		setmetatable(_object, nil)
		_questDefinitions_1[_exp] = _object
		print(`📜 Registered quest: {definition.name}`)
		return Result:ok(nil)
	end
	function QuestManager:startQuest(questId)
		local _questDefinitions = self.questDefinitions
		local _questId = questId
		local definition = _questDefinitions[_questId]
		if not definition then
			return Result:err(`Quest '{questId}' not found`)
		end
		local _activeQuests = self.activeQuests
		local _questId_1 = questId
		if _activeQuests[_questId_1] ~= nil then
			return Result:err("Quest is already active")
		end
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.activeQuests do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		if _size >= self.config.maxActiveQuests then
			return Result:err("Maximum active quests reached")
		end
		-- Check prerequisites
		if #definition.prerequisites > 0 then
			local _exp = definition.prerequisites
			-- ▼ ReadonlyArray.every ▼
			local _result = true
			local _callback = function(prereqId)
				local _completedQuests = self.completedQuests
				local _prereqId = prereqId
				return _completedQuests[_prereqId] ~= nil
			end
			for _k, _v in _exp do
				if not _callback(_v, _k - 1, _exp) then
					_result = false
					break
				end
			end
			-- ▲ ReadonlyArray.every ▲
			local prerequisitesMet = _result
			if not prerequisitesMet then
				return Result:err("Quest prerequisites not met")
			end
		end
		local quest = Quest.new(definition)
		-- Forward quest events
		quest:onEvent(function(event)
			self:handleQuestEvent(event)
		end)
		local startResult = quest:start()
		if startResult:isError() then
			return Result:err(startResult:getError())
		end
		local _activeQuests_1 = self.activeQuests
		local _questId_2 = questId
		_activeQuests_1[_questId_2] = quest
		return Result:ok(quest)
	end
	function QuestManager:completeQuest(questId)
		local _activeQuests = self.activeQuests
		local _questId = questId
		local quest = _activeQuests[_questId]
		if not quest then
			return Result:err("Quest is not active")
		end
		local result = quest:complete()
		if result:isOk() then
			local _completedQuests = self.completedQuests
			local _questId_1 = questId
			_completedQuests[_questId_1] = true
			-- Remove from active if not repeatable
			local definition = quest:getDefinition()
			if not definition.isRepeatable then
				local _activeQuests_1 = self.activeQuests
				local _questId_2 = questId
				_activeQuests_1[_questId_2] = nil
			end
		end
		return result
	end
	function QuestManager:failQuest(questId, reason)
		local _activeQuests = self.activeQuests
		local _questId = questId
		local quest = _activeQuests[_questId]
		if not quest then
			return Result:err("Quest is not active")
		end
		local result = quest:fail(reason)
		if result:isOk() then
			local _activeQuests_1 = self.activeQuests
			local _questId_1 = questId
			_activeQuests_1[_questId_1] = nil
		end
		return result
	end
	function QuestManager:abandonQuest(questId)
		local _activeQuests = self.activeQuests
		local _questId = questId
		local quest = _activeQuests[_questId]
		if not quest then
			return Result:err("Quest is not active")
		end
		local result = quest:abandon()
		if result:isOk() then
			local _activeQuests_1 = self.activeQuests
			local _questId_1 = questId
			_activeQuests_1[_questId_1] = nil
		end
		return result
	end
	function QuestManager:updateQuestObjective(questId, objectiveId, increment)
		if increment == nil then
			increment = 1
		end
		local _activeQuests = self.activeQuests
		local _questId = questId
		local quest = _activeQuests[_questId]
		if not quest then
			return Result:err("Quest is not active")
		end
		return quest:updateObjective(objectiveId, increment)
	end
	function QuestManager:processGameEvent(eventType, data)
		if not self.config.enableAutoProgress then
			return nil
		end
		local _exp = self.activeQuests
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(quest)
			local definition = quest:getDefinition()
			local progress = quest:getProgress()
			local _exp_1 = progress.objectives
			-- ▼ ReadonlyMap.forEach ▼
			local _callback_1 = function(objective)
				if objective.isCompleted then
					return nil
				end
				-- Check if this event matches the objective
				if self:doesEventMatchObjective(eventType, data, objective) then
					quest:updateObjective(objective.id, 1)
				end
			end
			for _k, _v in _exp_1 do
				_callback_1(_v, _k, _exp_1)
			end
			-- ▲ ReadonlyMap.forEach ▲
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
	end
	function QuestManager:getQuest(questId)
		local _activeQuests = self.activeQuests
		local _questId = questId
		return _activeQuests[_questId]
	end
	function QuestManager:activeQuestsToArray()
		local quests = {}
		local _exp = self.activeQuests
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(quest)
			local _quest = quest
			table.insert(quests, _quest)
			return #quests
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return quests
	end
	function QuestManager:getActiveQuests()
		return self:activeQuestsToArray()
	end
	function QuestManager:getQuestsByType(questType)
		local _exp = self:activeQuestsToArray()
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(quest)
			return quest:getDefinition().type == questType
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function QuestManager:getAvailableQuests()
		local available = {}
		local _exp = self.questDefinitions
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(definition)
			-- Skip if already active or completed (and not repeatable)
			local _activeQuests = self.activeQuests
			local _id = definition.id
			if _activeQuests[_id] ~= nil then
				return nil
			end
			local _completedQuests = self.completedQuests
			local _id_1 = definition.id
			local _condition = _completedQuests[_id_1] ~= nil
			if _condition then
				_condition = not definition.isRepeatable
			end
			if _condition then
				return nil
			end
			-- Check prerequisites
			local _exp_1 = definition.prerequisites
			-- ▼ ReadonlyArray.every ▼
			local _result = true
			local _callback_1 = function(prereqId)
				local _completedQuests_1 = self.completedQuests
				local _prereqId = prereqId
				return _completedQuests_1[_prereqId] ~= nil
			end
			for _k, _v in _exp_1 do
				if not _callback_1(_v, _k - 1, _exp_1) then
					_result = false
					break
				end
			end
			-- ▲ ReadonlyArray.every ▲
			local prerequisitesMet = _result
			if prerequisitesMet then
				local _definition = definition
				table.insert(available, _definition)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return available
	end
	function QuestManager:checkExpiredQuests()
		local expiredQuestIds = {}
		local _exp = self.activeQuests
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(quest, questId)
			if quest:checkExpiration() then
				local _questId = questId
				table.insert(expiredQuestIds, _questId)
				local _activeQuests = self.activeQuests
				local _questId_1 = questId
				_activeQuests[_questId_1] = nil
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return expiredQuestIds
	end
	function QuestManager:updateConfig(newConfig)
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		for _k, _v in newConfig do
			_object[_k] = _v
		end
		self.config = _object
	end
	function QuestManager:onEvent(callback)
		local _eventCallbacks = self.eventCallbacks
		local _callback = callback
		table.insert(_eventCallbacks, _callback)
		return function()
			local _eventCallbacks_1 = self.eventCallbacks
			local _callback_1 = callback
			local index = (table.find(_eventCallbacks_1, _callback_1) or 0) - 1
			if index >= 0 then
				table.remove(self.eventCallbacks, index + 1)
			end
		end
	end
	function QuestManager:getReport()
		local report = "📜 Quest Manager Report\n"
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.questDefinitions do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Registered Quests: {_size}\n`
		-- ▼ ReadonlyMap.size ▼
		local _size_1 = 0
		for _ in self.activeQuests do
			_size_1 += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Active Quests: {_size_1}\n`
		-- ▼ ReadonlySet.size ▼
		local _size_2 = 0
		for _ in self.completedQuests do
			_size_2 += 1
		end
		-- ▲ ReadonlySet.size ▲
		report ..= `Completed Quests: {_size_2}\n`
		report ..= `Available Quests: {#self:getAvailableQuests()}\n\n`
		-- Active quests breakdown
		-- ▼ ReadonlyMap.size ▼
		local _size_3 = 0
		for _ in self.activeQuests do
			_size_3 += 1
		end
		-- ▲ ReadonlyMap.size ▲
		if _size_3 > 0 then
			report ..= "📋 Active Quests:\n"
			local _exp = self.activeQuests
			-- ▼ ReadonlyMap.forEach ▼
			local _callback = function(quest)
				local definition = quest:getDefinition()
				local completion = quest:getCompletionPercentage()
				report ..= `  {definition.name}: {string.format("%.1f", completion)}% complete\n`
			end
			for _k, _v in _exp do
				_callback(_v, _k, _exp)
			end
			-- ▲ ReadonlyMap.forEach ▲
			report ..= "\n"
		end
		-- Quest types breakdown
		local typeCount = {}
		local _exp = self.activeQuests
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(quest)
			local questType = quest:getDefinition().type
			local _condition = typeCount[questType]
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = 0
			end
			typeCount[questType] = _condition + 1
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		if self:getObjectKeysCount(typeCount) > 0 then
			report ..= "📊 Active Quests by Type:\n"
			-- Manually check common quest types
			local _value = typeCount[QuestType.Main]
			if _value ~= 0 and _value == _value and _value then
				report ..= `  {QuestType.Main}: {typeCount[QuestType.Main]}\n`
			end
			local _value_1 = typeCount[QuestType.Side]
			if _value_1 ~= 0 and _value_1 == _value_1 and _value_1 then
				report ..= `  {QuestType.Side}: {typeCount[QuestType.Side]}\n`
			end
			local _value_2 = typeCount[QuestType.Daily]
			if _value_2 ~= 0 and _value_2 == _value_2 and _value_2 then
				report ..= `  {QuestType.Daily}: {typeCount[QuestType.Daily]}\n`
			end
			local _value_3 = typeCount[QuestType.Weekly]
			if _value_3 ~= 0 and _value_3 == _value_3 and _value_3 then
				report ..= `  {QuestType.Weekly}: {typeCount[QuestType.Weekly]}\n`
			end
			local _value_4 = typeCount[QuestType.Achievement]
			if _value_4 ~= 0 and _value_4 == _value_4 and _value_4 then
				report ..= `  {QuestType.Achievement}: {typeCount[QuestType.Achievement]}\n`
			end
			local _value_5 = typeCount[QuestType.Tutorial]
			if _value_5 ~= 0 and _value_5 == _value_5 and _value_5 then
				report ..= `  {QuestType.Tutorial}: {typeCount[QuestType.Tutorial]}\n`
			end
			local _value_6 = typeCount[QuestType.Event]
			if _value_6 ~= 0 and _value_6 == _value_6 and _value_6 then
				report ..= `  {QuestType.Event}: {typeCount[QuestType.Event]}\n`
			end
		end
		return report
	end
	function QuestManager:getObjectKeysCount(obj)
		-- Simple count by checking known properties
		local count = 0
		if obj[QuestType.Main] ~= nil then
			count += 1
		end
		if obj[QuestType.Side] ~= nil then
			count += 1
		end
		if obj[QuestType.Daily] ~= nil then
			count += 1
		end
		if obj[QuestType.Weekly] ~= nil then
			count += 1
		end
		if obj[QuestType.Achievement] ~= nil then
			count += 1
		end
		if obj[QuestType.Tutorial] ~= nil then
			count += 1
		end
		if obj[QuestType.Event] ~= nil then
			count += 1
		end
		return count
	end
	function QuestManager:handleQuestEvent(event)
		-- Notify global listeners
		local _exp = self.eventCallbacks
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(callback)
			TS.try(function()
				callback(event)
			end, function(error)
				warn(`Quest event callback failed: {error}`)
			end)
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Handle quest completion
		if event.type == "completed" then
			local _completedQuests = self.completedQuests
			local _questId = event.questId
			_completedQuests[_questId] = true
			-- Check for auto-start quests
			if self.config.enableAutoProgress then
				self:checkAutoStartQuests()
			end
		end
	end
	function QuestManager:checkAutoStartQuests()
		local available = self:getAvailableQuests()
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(definition)
			local _condition = definition.autoStart
			if _condition then
				-- ▼ ReadonlyMap.size ▼
				local _size = 0
				for _ in self.activeQuests do
					_size += 1
				end
				-- ▲ ReadonlyMap.size ▲
				_condition = _size < self.config.maxActiveQuests
			end
			if _condition then
				self:startQuest(definition.id)
			end
		end
		for _k, _v in available do
			_callback(_v, _k - 1, available)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function QuestManager:doesEventMatchObjective(eventType, data, objective)
		local _exp = objective.type
		repeat
			if _exp == (ObjectiveType.KillTarget) then
				return eventType == "entity_killed" and data.entityId == objective.targetId
			end
			if _exp == (ObjectiveType.CollectItem) then
				return eventType == "item_collected" and data.itemId == objective.targetId
			end
			if _exp == (ObjectiveType.ReachLocation) then
				return eventType == "location_reached" and data.locationId == objective.targetId
			end
			if _exp == (ObjectiveType.TalkToNPC) then
				return eventType == "npc_talked" and data.npcId == objective.targetId
			end
			if _exp == (ObjectiveType.UseItem) then
				return eventType == "item_used" and data.itemId == objective.targetId
			end
			if _exp == (ObjectiveType.Craft) then
				return eventType == "item_crafted" and data.itemId == objective.targetId
			end
			return false
		until true
	end
	function QuestManager:loadDefaultQuests()
		-- Load some default quest definitions for testing
		local defaultQuests = { {
			id = "tutorial_welcome",
			name = "Welcome to the World",
			description = "Complete your first tutorial objectives",
			type = QuestType.Tutorial,
			category = "Getting Started",
			level = 1,
			isRepeatable = false,
			prerequisites = {},
			objectives = { {
				id = "move_around",
				type = ObjectiveType.ReachLocation,
				description = "Move to the marked location",
				targetId = "tutorial_location_1",
				targetCount = 1,
				currentCount = 0,
				isOptional = false,
				isCompleted = false,
				isHidden = false,
			}, {
				id = "collect_first_item",
				type = ObjectiveType.CollectItem,
				description = "Collect your first item",
				targetId = "tutorial_coin",
				targetCount = 1,
				currentCount = 0,
				isOptional = false,
				isCompleted = false,
				isHidden = false,
			} },
			rewards = { {
				type = "experience",
				experiencePoints = 100,
			}, {
				type = "item",
				itemId = "starter_sword",
				quantity = 1,
			} },
			autoStart = true,
			autoComplete = true,
			tags = { "tutorial", "beginner" },
		} }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(quest)
			self:registerQuest(quest)
		end
		for _k, _v in defaultQuests do
			_callback(_v, _k - 1, defaultQuests)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
end
-- Global quest management functions for easy access
local QuestSystem = {
	getInstance = function()
		return QuestManager:getInstance()
	end,
	startQuest = function(questId)
		return QuestManager:getInstance():startQuest(questId)
	end,
	getQuest = function(questId)
		return QuestManager:getInstance():getQuest(questId)
	end,
	processGameEvent = function(eventType, data)
		return QuestManager:getInstance():processGameEvent(eventType, data)
	end,
	getReport = function()
		return QuestManager:getInstance():getReport()
	end,
}
return {
	Quest = Quest,
	QuestManager = QuestManager,
	QuestSystem = QuestSystem,
}
