-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local useState = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react").useState
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Button = _core.Button
local VerticalFrame = _core.VerticalFrame
local WorldTestingPanel = TS.import(script, script.Parent, "WorldTestingPanel").WorldTestingPanel
local function ZIndexDemo()
	local showVoiceChat, setShowVoiceChat = useState(false)
	local showWorldTesting, setShowWorldTesting = useState(false)
	return React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
	}, React.createElement(VerticalFrame, {
		size = UDim2.new(0, 200, 0, 150),
		position = UDim2.new(0, 20, 0, 20),
		spacing = 8,
		padding = 12,
		backgroundTransparency = 0.1,
	}, React.createElement(Button, {
		text = "Toggle Voice Chat",
		onClick = function()
			return setShowVoiceChat(not showVoiceChat)
		end,
	}), React.createElement(Button, {
		text = "Toggle World Testing",
		onClick = function()
			return setShowWorldTesting(not showWorldTesting)
		end,
	}), React.createElement(Button, {
		text = "Show Both",
		onClick = function()
			setShowVoiceChat(true)
			setShowWorldTesting(true)
		end,
	}), React.createElement(Button, {
		text = "Hide Both",
		onClick = function()
			setShowVoiceChat(false)
			setShowWorldTesting(false)
		end,
	})), React.createElement(WorldTestingPanel))
end
return {
	ZIndexDemo = ZIndexDemo,
}
