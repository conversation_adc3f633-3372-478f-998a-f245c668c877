import { RunService } from "@rbxts/services";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/BrandedTypes";
import { BaseService } from "../foundation/BaseService";

interface PerformanceMetrics {
	frameTime: number;
	fps: number;
	memoryUsage: number;
	heartbeatFrequency: number;
	renderStepTime: number;
	averageFrameTime: number;
	peakMemoryUsage: number;
	gcCount: number;
}

interface PerformanceAlert {
	type: "fps" | "memory" | "frametime";
	severity: "warning" | "critical";
	message: string;
	value: number;
	threshold: number;
	timestamp: number;
}

interface PerformanceThresholds {
	minFps: number;
	maxFrameTime: number;
	maxMemoryUsage: number;
	warningMemoryUsage: number;
}

interface ProfilerEntry {
	name: string;
	startTime: number;
	endTime?: number;
	duration?: number;
	category?: string;
}

/**
 * Performance monitoring system for tracking frame rates, memory usage, and profiling
 * Provides real-time performance metrics and alerts for optimization
 */
export class PerformanceMonitor extends BaseService {
	private static instance: PerformanceMonitor;

	private isMonitoring = false;
	private heartbeatConnection?: RBXScriptConnection;
	private renderStepConnection?: RBXScriptConnection;

	private frameHistory: number[] = [];
	private memoryHistory: number[] = [];
	private readonly maxHistorySize = 300; // 5 seconds at 60fps

	private activeProfiles: Map<string, ProfilerEntry> = new Map();
	private completedProfiles: ProfilerEntry[] = [];
	private maxProfileHistory = 1000;

	private metrics: PerformanceMetrics = {
		frameTime: 0,
		fps: 0,
		memoryUsage: 0,
		heartbeatFrequency: 0,
		renderStepTime: 0,
		averageFrameTime: 0,
		peakMemoryUsage: 0,
		gcCount: 0,
	};

	private thresholds: PerformanceThresholds = {
		minFps: 30,
		maxFrameTime: 33.33, // 30fps = 33.33ms per frame
		maxMemoryUsage: 1024, // 1GB in MB
		warningMemoryUsage: 512, // 512MB warning
	};

	private alerts: PerformanceAlert[] = [];
	private maxAlertHistory = 100;
	private alertCallbacks: ((alert: PerformanceAlert) => void)[] = [];

	private lastGCCount = 0;
	private gcInterval = 0;
	private lastGCTime = 0;

	constructor() {
		super("PerformanceMonitor");
	}

	public static getInstance(): PerformanceMonitor {
		if (!PerformanceMonitor.instance) {
			PerformanceMonitor.instance = new PerformanceMonitor();
		}
		return PerformanceMonitor.instance;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		try {
			this.startMonitoring();
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to initialize PerformanceMonitor: ${error}`));
		}
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.stopMonitoring();
		return Result.ok(undefined);
	}

	/**
	 * Start performance monitoring
	 */
	public startMonitoring(): void {
		if (this.isMonitoring) return;

		this.isMonitoring = true;

		// Monitor heartbeat for frame time and FPS
		this.heartbeatConnection = RunService.Heartbeat.Connect((deltaTime) => {
			this.updateFrameMetrics(deltaTime);
		});

		// Monitor render step time (client-side only)
		if (RunService.IsClient()) {
			this.renderStepConnection = RunService.RenderStepped.Connect((deltaTime) => {
				this.metrics.renderStepTime = deltaTime * 1000; // Convert to ms
			});
		}

		print("🔍 Performance monitoring started");
	}

	/**
	 * Stop performance monitoring
	 */
	public stopMonitoring(): void {
		if (!this.isMonitoring) return;

		this.isMonitoring = false;

		if (this.heartbeatConnection) {
			this.heartbeatConnection.Disconnect();
			this.heartbeatConnection = undefined;
		}

		if (this.renderStepConnection) {
			this.renderStepConnection.Disconnect();
			this.renderStepConnection = undefined;
		}

		print("🔍 Performance monitoring stopped");
	}

	/**
	 * Get current performance metrics
	 */
	public getMetrics(): PerformanceMetrics {
		return { ...this.metrics };
	}

	/**
	 * Get performance alerts
	 */
	public getAlerts(): PerformanceAlert[] {
		return [...this.alerts];
	}

	/**
	 * Clear performance alerts
	 */
	public clearAlerts(): void {
		this.alerts = [];
	}

	/**
	 * Set performance thresholds for alerts
	 */
	public setThresholds(thresholds: Partial<PerformanceThresholds>): void {
		this.thresholds = { ...this.thresholds, ...thresholds };
	}

	/**
	 * Add callback for performance alerts
	 */
	public onAlert(callback: (alert: PerformanceAlert) => void): () => void {
		this.alertCallbacks.push(callback);

		// Return unsubscribe function
		return () => {
			const index = this.alertCallbacks.indexOf(callback);
			if (index >= 0) {
				this.alertCallbacks.remove(index);
			}
		};
	}

	/**
	 * Start profiling a code section
	 */
	public startProfile(name: string, category?: string): void {
		const entry: ProfilerEntry = {
			name,
			startTime: tick(),
			category,
		};

		this.activeProfiles.set(name, entry);
	}

	/**
	 * End profiling a code section
	 */
	public endProfile(name: string): number | undefined {
		const entry = this.activeProfiles.get(name);
		if (!entry) {
			warn(`Profile "${name}" not found or already ended`);
			return undefined;
		}

		const endTime = tick();
		entry.endTime = endTime;
		entry.duration = (endTime - entry.startTime) * 1000; // Convert to ms

		this.activeProfiles.delete(name);
		this.completedProfiles.push(entry);

		// Limit history size
		if (this.completedProfiles.size() > this.maxProfileHistory) {
			this.completedProfiles.remove(0);
		}

		return entry.duration;
	}

	/**
	 * Profile a function execution
	 */
	public profile<T>(name: string, fn: () => T, category?: string): T {
		this.startProfile(name, category);
		const result = fn();
		this.endProfile(name);
		return result;
	}

	/**
	 * Profile an async function execution
	 */
	public profileAsync<T>(name: string, fn: () => Promise<T>, category?: string): Promise<T> {
		this.startProfile(name, category);
		return fn()
			.then((result) => {
				this.endProfile(name);
				return result;
			})
			.catch((err) => {
				this.endProfile(name);
				throw err;
			});
	}

	/**
	 * Get profiling results
	 */
	public getProfiles(category?: string): ProfilerEntry[] {
		if (!category) {
			return [...this.completedProfiles];
		}

		return this.completedProfiles.filter((entry) => entry.category === category);
	}

	/**
	 * Get profiling statistics
	 */
	public getProfileStats(
		name?: string,
		category?: string,
	):
		| {
				count: number;
				totalTime: number;
				averageTime: number;
				minTime: number;
				maxTime: number;
		  }
		| undefined {
		let profiles = this.completedProfiles;

		if (name) {
			profiles = profiles.filter((entry) => entry.name === name);
		}

		if (category) {
			profiles = profiles.filter((entry) => entry.category === category);
		}

		if (profiles.size() === 0) {
			return undefined;
		}

		const durations = profiles.map((entry) => entry.duration || 0);
		const totalTime = durations.reduce((sum, duration) => sum + duration, 0);

		return {
			count: profiles.size(),
			totalTime,
			averageTime: totalTime / profiles.size(),
			minTime: math.min(...durations),
			maxTime: math.max(...durations),
		};
	}

	/**
	 * Clear profiling history
	 */
	public clearProfiles(): void {
		this.completedProfiles = [];
		this.activeProfiles.clear();
	}

	/**
	 * Get memory usage in MB
	 */
	public getMemoryUsage(): number {
		const stats = game.GetService("Stats");
		// This is an approximation - actual memory tracking in Roblox is limited
		try {
			return stats.GetTotalMemoryUsageMb();
		} catch {
			return 0;
		}
	}

	/**
	 * Force garbage collection (if available)
	 */
	public forceGC(): void {
		// Note: Roblox doesn't provide direct GC control, but we can track it
		const currentTime = tick();
		if (currentTime - this.lastGCTime > 1) {
			// Only track once per second
			this.lastGCTime = currentTime;
			this.metrics.gcCount += 1;
		}
	}

	private updateFrameMetrics(deltaTime: number): void {
		// Update frame time
		const frameTimeMs = deltaTime * 1000;
		this.metrics.frameTime = frameTimeMs;

		// Update FPS
		this.metrics.fps = 1 / deltaTime;

		// Update heartbeat frequency
		this.metrics.heartbeatFrequency = this.metrics.fps;

		// Update frame history for averaging
		this.frameHistory.push(frameTimeMs);
		if (this.frameHistory.size() > this.maxHistorySize) {
			this.frameHistory.remove(0);
		}

		// Calculate average frame time
		if (this.frameHistory.size() > 0) {
			const total = this.frameHistory.reduce((sum, time) => sum + time, 0);
			this.metrics.averageFrameTime = total / this.frameHistory.size();
		}

		// Update memory metrics
		this.updateMemoryMetrics();

		// Check for performance issues
		this.checkThresholds();
	}

	private updateMemoryMetrics(): void {
		const currentMemory = this.getMemoryUsage();
		this.metrics.memoryUsage = currentMemory;

		// Track peak memory
		if (currentMemory > this.metrics.peakMemoryUsage) {
			this.metrics.peakMemoryUsage = currentMemory;
		}

		// Update memory history
		this.memoryHistory.push(currentMemory);
		if (this.memoryHistory.size() > this.maxHistorySize) {
			this.memoryHistory.remove(0);
		}
	}

	private checkThresholds(): void {
		const now = tick();

		// Check FPS threshold
		if (this.metrics.fps < this.thresholds.minFps) {
			this.addAlert({
				type: "fps",
				severity: this.metrics.fps < this.thresholds.minFps * 0.8 ? "critical" : "warning",
				message: `Low FPS detected: ${math.floor(this.metrics.fps)}`,
				value: this.metrics.fps,
				threshold: this.thresholds.minFps,
				timestamp: now,
			});
		}

		// Check frame time threshold
		if (this.metrics.frameTime > this.thresholds.maxFrameTime) {
			this.addAlert({
				type: "frametime",
				severity: this.metrics.frameTime > this.thresholds.maxFrameTime * 1.5 ? "critical" : "warning",
				message: `High frame time: ${string.format("%.2f", this.metrics.frameTime)}ms`,
				value: this.metrics.frameTime,
				threshold: this.thresholds.maxFrameTime,
				timestamp: now,
			});
		}

		// Check memory threshold
		if (this.metrics.memoryUsage > this.thresholds.warningMemoryUsage) {
			const severity = this.metrics.memoryUsage > this.thresholds.maxMemoryUsage ? "critical" : "warning";
			this.addAlert({
				type: "memory",
				severity,
				message: `High memory usage: ${string.format("%.1f", this.metrics.memoryUsage)}MB`,
				value: this.metrics.memoryUsage,
				threshold:
					severity === "critical" ? this.thresholds.maxMemoryUsage : this.thresholds.warningMemoryUsage,
				timestamp: now,
			});
		}
	}

	private addAlert(alert: PerformanceAlert): void {
		// Prevent spam by checking if similar alert was recently added
		const recentSimilar = this.alerts.find(
			(existing) =>
				existing.type === alert.type &&
				existing.severity === alert.severity &&
				alert.timestamp - existing.timestamp < 5, // 5 seconds
		);

		if (recentSimilar) return;

		this.alerts.push(alert);

		// Limit alert history
		if (this.alerts.size() > this.maxAlertHistory) {
			this.alerts.remove(0);
		}

		// Notify callbacks
		this.alertCallbacks.forEach((callback) => {
			try {
				callback(alert);
			} catch (error) {
				warn(`Performance alert callback failed: ${error}`);
			}
		});
	}

	/**
	 * Get a performance report
	 */
	public getReport(): string {
		const metrics = this.getMetrics();
		const alerts = this.getAlerts();

		let report = "📊 Performance Report\n";
		report += `FPS: ${string.format("%.1f", metrics.fps)} | Frame Time: ${string.format("%.2f", metrics.frameTime)}ms (avg: ${string.format("%.2f", metrics.averageFrameTime)}ms)\n`;
		report += `Memory: ${string.format("%.1f", metrics.memoryUsage)}MB (peak: ${string.format("%.1f", metrics.peakMemoryUsage)}MB)\n`;
		report += `GC Count: ${metrics.gcCount}\n`;

		if (alerts.size() > 0) {
			report += `\n⚠️ Alerts (${alerts.size()}):\n`;
			alerts.forEach((alert) => {
				report += `${alert.severity.upper()}: ${alert.message}\n`;
			});
		}

		const profileStats = this.getProfileStats();
		if (profileStats) {
			report += `\n📈 Profiling: ${profileStats.count} samples, avg: ${string.format("%.2f", profileStats.averageTime)}ms\n`;
		}

		return report;
	}
}

// Global performance monitoring functions for easy access
export const Performance = {
	getInstance: () => PerformanceMonitor.getInstance(),
	start: () => PerformanceMonitor.getInstance().startMonitoring(),
	stop: () => PerformanceMonitor.getInstance().stopMonitoring(),
	getMetrics: () => PerformanceMonitor.getInstance().getMetrics(),
	profile: <T>(name: string, fn: () => T, category?: string) =>
		PerformanceMonitor.getInstance().profile(name, fn, category),
	profileAsync: <T>(name: string, fn: () => Promise<T>, category?: string) =>
		PerformanceMonitor.getInstance().profileAsync(name, fn, category),
	getReport: () => PerformanceMonitor.getInstance().getReport(),
};
