-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local RunService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").RunService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
--[[
	*
	 * Performance monitoring system for tracking frame rates, memory usage, and profiling
	 * Provides real-time performance metrics and alerts for optimization
	 
]]
local PerformanceMonitor
do
	local super = BaseService
	PerformanceMonitor = setmetatable({}, {
		__tostring = function()
			return "PerformanceMonitor"
		end,
		__index = super,
	})
	PerformanceMonitor.__index = PerformanceMonitor
	function PerformanceMonitor.new(...)
		local self = setmetatable({}, PerformanceMonitor)
		return self:constructor(...) or self
	end
	function PerformanceMonitor:constructor()
		super.constructor(self, "PerformanceMonitor")
		self.isMonitoring = false
		self.frameHistory = {}
		self.memoryHistory = {}
		self.maxHistorySize = 300
		self.activeProfiles = {}
		self.completedProfiles = {}
		self.maxProfileHistory = 1000
		self.metrics = {
			frameTime = 0,
			fps = 0,
			memoryUsage = 0,
			heartbeatFrequency = 0,
			renderStepTime = 0,
			averageFrameTime = 0,
			peakMemoryUsage = 0,
			gcCount = 0,
		}
		self.thresholds = {
			minFps = 30,
			maxFrameTime = 33.33,
			maxMemoryUsage = 1024,
			warningMemoryUsage = 512,
		}
		self.alerts = {}
		self.maxAlertHistory = 100
		self.alertCallbacks = {}
		self.lastGCCount = 0
		self.gcInterval = 0
		self.lastGCTime = 0
	end
	function PerformanceMonitor:getInstance()
		if not PerformanceMonitor.instance then
			PerformanceMonitor.instance = PerformanceMonitor.new()
		end
		return PerformanceMonitor.instance
	end
	PerformanceMonitor.onInitialize = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			self:startMonitoring()
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize PerformanceMonitor: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	PerformanceMonitor.onShutdown = TS.async(function(self)
		self:stopMonitoring()
		return Result:ok(nil)
	end)
	function PerformanceMonitor:startMonitoring()
		if self.isMonitoring then
			return nil
		end
		self.isMonitoring = true
		-- Monitor heartbeat for frame time and FPS
		self.heartbeatConnection = RunService.Heartbeat:Connect(function(deltaTime)
			self:updateFrameMetrics(deltaTime)
		end)
		-- Monitor render step time (client-side only)
		if RunService:IsClient() then
			self.renderStepConnection = RunService.RenderStepped:Connect(function(deltaTime)
				self.metrics.renderStepTime = deltaTime * 1000
			end)
		end
		print("🔍 Performance monitoring started")
	end
	function PerformanceMonitor:stopMonitoring()
		if not self.isMonitoring then
			return nil
		end
		self.isMonitoring = false
		if self.heartbeatConnection then
			self.heartbeatConnection:Disconnect()
			self.heartbeatConnection = nil
		end
		if self.renderStepConnection then
			self.renderStepConnection:Disconnect()
			self.renderStepConnection = nil
		end
		print("🔍 Performance monitoring stopped")
	end
	function PerformanceMonitor:getMetrics()
		local _object = table.clone(self.metrics)
		setmetatable(_object, nil)
		return _object
	end
	function PerformanceMonitor:getAlerts()
		local _array = {}
		local _length = #_array
		local _array_1 = self.alerts
		table.move(_array_1, 1, #_array_1, _length + 1, _array)
		return _array
	end
	function PerformanceMonitor:clearAlerts()
		self.alerts = {}
	end
	function PerformanceMonitor:setThresholds(thresholds)
		local _object = table.clone(self.thresholds)
		setmetatable(_object, nil)
		for _k, _v in thresholds do
			_object[_k] = _v
		end
		self.thresholds = _object
	end
	function PerformanceMonitor:onAlert(callback)
		local _alertCallbacks = self.alertCallbacks
		local _callback = callback
		table.insert(_alertCallbacks, _callback)
		-- Return unsubscribe function
		return function()
			local _alertCallbacks_1 = self.alertCallbacks
			local _callback_1 = callback
			local index = (table.find(_alertCallbacks_1, _callback_1) or 0) - 1
			if index >= 0 then
				table.remove(self.alertCallbacks, index + 1)
			end
		end
	end
	function PerformanceMonitor:startProfile(name, category)
		local entry = {
			name = name,
			startTime = tick(),
			category = category,
		}
		local _activeProfiles = self.activeProfiles
		local _name = name
		_activeProfiles[_name] = entry
	end
	function PerformanceMonitor:endProfile(name)
		local _activeProfiles = self.activeProfiles
		local _name = name
		local entry = _activeProfiles[_name]
		if not entry then
			warn(`Profile "{name}" not found or already ended`)
			return nil
		end
		local endTime = tick()
		entry.endTime = endTime
		entry.duration = (endTime - entry.startTime) * 1000
		local _activeProfiles_1 = self.activeProfiles
		local _name_1 = name
		_activeProfiles_1[_name_1] = nil
		local _exp = self.completedProfiles
		table.insert(_exp, entry)
		-- Limit history size
		if #self.completedProfiles > self.maxProfileHistory then
			table.remove(self.completedProfiles, 1)
		end
		return entry.duration
	end
	function PerformanceMonitor:profile(name, fn, category)
		self:startProfile(name, category)
		local result = fn()
		self:endProfile(name)
		return result
	end
	function PerformanceMonitor:profileAsync(name, fn, category)
		self:startProfile(name, category)
		return fn():andThen(function(result)
			self:endProfile(name)
			return result
		end):catch(function(err)
			self:endProfile(name)
			error(err)
		end)
	end
	function PerformanceMonitor:getProfiles(category)
		if not (category ~= "" and category) then
			local _array = {}
			local _length = #_array
			local _array_1 = self.completedProfiles
			table.move(_array_1, 1, #_array_1, _length + 1, _array)
			return _array
		end
		local _exp = self.completedProfiles
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(entry)
			return entry.category == category
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function PerformanceMonitor:getProfileStats(name, category)
		local profiles = self.completedProfiles
		if name ~= "" and name then
			-- ▼ ReadonlyArray.filter ▼
			local _newValue = {}
			local _callback = function(entry)
				return entry.name == name
			end
			local _length = 0
			for _k, _v in profiles do
				if _callback(_v, _k - 1, profiles) == true then
					_length += 1
					_newValue[_length] = _v
				end
			end
			-- ▲ ReadonlyArray.filter ▲
			profiles = _newValue
		end
		if category ~= "" and category then
			-- ▼ ReadonlyArray.filter ▼
			local _newValue = {}
			local _callback = function(entry)
				return entry.category == category
			end
			local _length = 0
			for _k, _v in profiles do
				if _callback(_v, _k - 1, profiles) == true then
					_length += 1
					_newValue[_length] = _v
				end
			end
			-- ▲ ReadonlyArray.filter ▲
			profiles = _newValue
		end
		if #profiles == 0 then
			return nil
		end
		-- ▼ ReadonlyArray.map ▼
		local _newValue = table.create(#profiles)
		local _callback = function(entry)
			local _condition = entry.duration
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = 0
			end
			return _condition
		end
		for _k, _v in profiles do
			_newValue[_k] = _callback(_v, _k - 1, profiles)
		end
		-- ▲ ReadonlyArray.map ▲
		local durations = _newValue
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback_1 = function(sum, duration)
			return sum + duration
		end
		for _i = 1, #durations do
			_result = _callback_1(_result, durations[_i], _i - 1, durations)
		end
		-- ▲ ReadonlyArray.reduce ▲
		local totalTime = _result
		return {
			count = #profiles,
			totalTime = totalTime,
			averageTime = totalTime / #profiles,
			minTime = math.min(unpack(durations)),
			maxTime = math.max(unpack(durations)),
		}
	end
	function PerformanceMonitor:clearProfiles()
		self.completedProfiles = {}
		table.clear(self.activeProfiles)
	end
	function PerformanceMonitor:getMemoryUsage()
		local stats = game:GetService("Stats")
		-- This is an approximation - actual memory tracking in Roblox is limited
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { stats:GetTotalMemoryUsageMb() }
		end, function()
			return TS.TRY_RETURN, { 0 }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	function PerformanceMonitor:forceGC()
		-- Note: Roblox doesn't provide direct GC control, but we can track it
		local currentTime = tick()
		if currentTime - self.lastGCTime > 1 then
			-- Only track once per second
			self.lastGCTime = currentTime
			self.metrics.gcCount += 1
		end
	end
	function PerformanceMonitor:updateFrameMetrics(deltaTime)
		-- Update frame time
		local frameTimeMs = deltaTime * 1000
		self.metrics.frameTime = frameTimeMs
		-- Update FPS
		self.metrics.fps = 1 / deltaTime
		-- Update heartbeat frequency
		self.metrics.heartbeatFrequency = self.metrics.fps
		-- Update frame history for averaging
		local _exp = self.frameHistory
		table.insert(_exp, frameTimeMs)
		if #self.frameHistory > self.maxHistorySize then
			table.remove(self.frameHistory, 1)
		end
		-- Calculate average frame time
		if #self.frameHistory > 0 then
			local _exp_1 = self.frameHistory
			-- ▼ ReadonlyArray.reduce ▼
			local _result = 0
			local _callback = function(sum, time)
				return sum + time
			end
			for _i = 1, #_exp_1 do
				_result = _callback(_result, _exp_1[_i], _i - 1, _exp_1)
			end
			-- ▲ ReadonlyArray.reduce ▲
			local total = _result
			self.metrics.averageFrameTime = total / #self.frameHistory
		end
		-- Update memory metrics
		self:updateMemoryMetrics()
		-- Check for performance issues
		self:checkThresholds()
	end
	function PerformanceMonitor:updateMemoryMetrics()
		local currentMemory = self:getMemoryUsage()
		self.metrics.memoryUsage = currentMemory
		-- Track peak memory
		if currentMemory > self.metrics.peakMemoryUsage then
			self.metrics.peakMemoryUsage = currentMemory
		end
		-- Update memory history
		local _exp = self.memoryHistory
		table.insert(_exp, currentMemory)
		if #self.memoryHistory > self.maxHistorySize then
			table.remove(self.memoryHistory, 1)
		end
	end
	function PerformanceMonitor:checkThresholds()
		local now = tick()
		-- Check FPS threshold
		if self.metrics.fps < self.thresholds.minFps then
			self:addAlert({
				type = "fps",
				severity = if self.metrics.fps < self.thresholds.minFps * 0.8 then "critical" else "warning",
				message = `Low FPS detected: {math.floor(self.metrics.fps)}`,
				value = self.metrics.fps,
				threshold = self.thresholds.minFps,
				timestamp = now,
			})
		end
		-- Check frame time threshold
		if self.metrics.frameTime > self.thresholds.maxFrameTime then
			self:addAlert({
				type = "frametime",
				severity = if self.metrics.frameTime > self.thresholds.maxFrameTime * 1.5 then "critical" else "warning",
				message = `High frame time: {string.format("%.2f", self.metrics.frameTime)}ms`,
				value = self.metrics.frameTime,
				threshold = self.thresholds.maxFrameTime,
				timestamp = now,
			})
		end
		-- Check memory threshold
		if self.metrics.memoryUsage > self.thresholds.warningMemoryUsage then
			local severity = if self.metrics.memoryUsage > self.thresholds.maxMemoryUsage then "critical" else "warning"
			self:addAlert({
				type = "memory",
				severity = severity,
				message = `High memory usage: {string.format("%.1f", self.metrics.memoryUsage)}MB`,
				value = self.metrics.memoryUsage,
				threshold = if severity == "critical" then self.thresholds.maxMemoryUsage else self.thresholds.warningMemoryUsage,
				timestamp = now,
			})
		end
	end
	function PerformanceMonitor:addAlert(alert)
		-- Prevent spam by checking if similar alert was recently added
		local _exp = self.alerts
		-- ▼ ReadonlyArray.find ▼
		local _callback = function(existing)
			return existing.type == alert.type and existing.severity == alert.severity and alert.timestamp - existing.timestamp < 5
		end
		local _result
		for _i, _v in _exp do
			if _callback(_v, _i - 1, _exp) == true then
				_result = _v
				break
			end
		end
		-- ▲ ReadonlyArray.find ▲
		local recentSimilar = _result
		if recentSimilar then
			return nil
		end
		local _alerts = self.alerts
		local _alert = alert
		table.insert(_alerts, _alert)
		-- Limit alert history
		if #self.alerts > self.maxAlertHistory then
			table.remove(self.alerts, 1)
		end
		-- Notify callbacks
		local _exp_1 = self.alertCallbacks
		-- ▼ ReadonlyArray.forEach ▼
		local _callback_1 = function(callback)
			TS.try(function()
				callback(alert)
			end, function(error)
				warn(`Performance alert callback failed: {error}`)
			end)
		end
		for _k, _v in _exp_1 do
			_callback_1(_v, _k - 1, _exp_1)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function PerformanceMonitor:getReport()
		local metrics = self:getMetrics()
		local alerts = self:getAlerts()
		local report = "📊 Performance Report\n"
		report ..= `FPS: {string.format("%.1f", metrics.fps)} | Frame Time: {string.format("%.2f", metrics.frameTime)}ms (avg: {string.format("%.2f", metrics.averageFrameTime)}ms)\n`
		report ..= `Memory: {string.format("%.1f", metrics.memoryUsage)}MB (peak: {string.format("%.1f", metrics.peakMemoryUsage)}MB)\n`
		report ..= `GC Count: {metrics.gcCount}\n`
		if #alerts > 0 then
			report ..= `\n⚠️ Alerts ({#alerts}):\n`
			-- ▼ ReadonlyArray.forEach ▼
			local _callback = function(alert)
				report ..= `{string.upper(alert.severity)}: {alert.message}\n`
			end
			for _k, _v in alerts do
				_callback(_v, _k - 1, alerts)
			end
			-- ▲ ReadonlyArray.forEach ▲
		end
		local profileStats = self:getProfileStats()
		if profileStats then
			report ..= `\n📈 Profiling: {profileStats.count} samples, avg: {string.format("%.2f", profileStats.averageTime)}ms\n`
		end
		return report
	end
end
-- Global performance monitoring functions for easy access
local Performance = {
	getInstance = function()
		return PerformanceMonitor:getInstance()
	end,
	start = function()
		return PerformanceMonitor:getInstance():startMonitoring()
	end,
	stop = function()
		return PerformanceMonitor:getInstance():stopMonitoring()
	end,
	getMetrics = function()
		return PerformanceMonitor:getInstance():getMetrics()
	end,
	profile = function(name, fn, category)
		return PerformanceMonitor:getInstance():profile(name, fn, category)
	end,
	profileAsync = function(name, fn, category)
		return PerformanceMonitor:getInstance():profileAsync(name, fn, category)
	end,
	getReport = function()
		return PerformanceMonitor:getInstance():getReport()
	end,
}
return {
	PerformanceMonitor = PerformanceMonitor,
	Performance = Performance,
}
