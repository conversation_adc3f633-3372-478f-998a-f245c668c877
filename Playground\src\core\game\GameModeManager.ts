import { StateManager } from "../state/StateManager";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/RobloxError";
import { StateAction } from "../state/interfaces/StateAction";

export enum GameMode {
	Playground = "Playground",
	CollectorArena = "CollectorArena",
}

export interface GameModeState {
	currentMode: GameMode;
	isGameActive: boolean;
	canSwitchMode: boolean;
}

export interface GameController {
	start(): Promise<Result<void, Error>>;
	stop(): Promise<Result<void, Error>>;
	cleanup(): Promise<Result<void, Error>>;
	getName(): string;
}

export class GameModeManager {
	private static instance: GameModeManager;
	private gameControllers = new Map<GameMode, GameController>();
	private currentController?: GameController;
	private currentMode: GameMode = GameMode.Playground;
	private isGameActive = false;
	private canSwitchMode = true;

	private constructor() {
		// Simple state management without StateManager for now
	}

	public static getInstance(): GameModeManager {
		if (!GameModeManager.instance) {
			GameModeManager.instance = new GameModeManager();
		}
		return GameModeManager.instance;
	}

	public registerGameController(mode: GameMode, controller: GameController): void {
		this.gameControllers.set(mode, controller);
		print(`🎮 [GameModeManager] Registered controller for ${mode}: ${controller.getName()}`);
	}

	public async switchToMode(mode: GameMode): Promise<Result<void, Error>> {
		if (!this.canSwitchMode) {
			return Result.err(createError("Cannot switch game mode while a game is active"));
		}

		if (this.currentMode === mode) {
			return Result.ok(undefined);
		}

		try {
			// Stop current game if active
			if (this.currentController && this.isGameActive) {
				const stopResult = await this.currentController.stop();
				if (stopResult.isError()) {
					return Result.err(createError(`Failed to stop current game: ${stopResult.getError().message}`));
				}
			}

			// Cleanup current controller
			if (this.currentController) {
				const cleanupResult = await this.currentController.cleanup();
				if (cleanupResult.isError()) {
					warn(
						`⚠️ [GameModeManager] Failed to cleanup current controller: ${cleanupResult.getError().message}`,
					);
				}
			}

			// Switch to new controller
			const newController = this.gameControllers.get(mode);
			if (!newController) {
				return Result.err(createError(`No controller registered for game mode: ${mode}`));
			}

			this.currentController = newController;
			this.currentMode = mode;
			this.isGameActive = false;
			this.canSwitchMode = true;

			print(`🎮 [GameModeManager] Switched to ${mode} mode`);
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to switch game mode: ${error}`));
		}
	}

	public async startCurrentGame(): Promise<Result<void, Error>> {
		if (this.isGameActive) {
			return Result.err(createError("A game is already active"));
		}

		if (!this.currentController) {
			return Result.err(createError("No game controller is active"));
		}

		try {
			this.canSwitchMode = false;

			const startResult = await this.currentController.start();
			if (startResult.isError()) {
				// Restore ability to switch modes if start failed
				this.canSwitchMode = true;
				return Result.err(createError(`Failed to start game: ${startResult.getError().message}`));
			}

			this.isGameActive = true;
			this.canSwitchMode = false;

			print(`🎮 [GameModeManager] Started ${this.currentMode} game`);
			return Result.ok(undefined);
		} catch (error) {
			// Restore ability to switch modes if error occurred
			this.canSwitchMode = true;
			return Result.err(createError(`Failed to start game: ${error}`));
		}
	}

	public async stopCurrentGame(): Promise<Result<void, Error>> {
		if (!this.isGameActive) {
			return Result.err(createError("No game is currently active"));
		}

		if (!this.currentController) {
			return Result.err(createError("No game controller is active"));
		}

		try {
			const stopResult = await this.currentController.stop();
			if (stopResult.isError()) {
				return Result.err(createError(`Failed to stop game: ${stopResult.getError().message}`));
			}

			this.isGameActive = false;
			this.canSwitchMode = true;

			print(`🎮 [GameModeManager] Stopped ${this.currentMode} game`);
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to stop game: ${error}`));
		}
	}

	public getCurrentMode(): GameMode {
		return this.currentMode;
	}

	public isGameActiveState(): boolean {
		return this.isGameActive;
	}

	public canSwitchModeState(): boolean {
		return this.canSwitchMode;
	}

	public getAvailableModes(): GameMode[] {
		const modes: GameMode[] = [];
		for (const [mode] of this.gameControllers) {
			modes.push(mode);
		}
		return modes;
	}
}
