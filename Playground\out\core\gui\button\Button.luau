-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local function Button(props)
	local hovered, setHovered = React.useState(false)
	local pressed, setPressed = React.useState(false)
	local getBackgroundColor = function()
		if props.disabled then
			return COLORS.bg.secondary
		end
		if props.loading then
			return COLORS.bg.surface
		end
		local variant = props.variant or "primary"
		if pressed then
			repeat
				if variant == "primary" then
					return COLORS["primary-dark"]
				end
				if variant == "success" then
					return COLORS["success-dark"]
				end
				if variant == "warning" then
					return COLORS["warning-dark"]
				end
				if variant == "error" then
					return COLORS["error-dark"]
				end
				if variant == "ghost" then
					return COLORS.bg.hover
				end
				if variant == "outline" then
					return COLORS.bg.hover
				end
				return COLORS["primary-dark"]
			until true
		end
		if hovered then
			repeat
				if variant == "primary" then
					return COLORS["primary-hover"]
				end
				if variant == "success" then
					return COLORS["success-hover"]
				end
				if variant == "warning" then
					return COLORS["warning-hover"]
				end
				if variant == "error" then
					return COLORS["error-hover"]
				end
				if variant == "ghost" then
					return COLORS.bg.hover
				end
				if variant == "outline" then
					return COLORS.bg.hover
				end
				return COLORS["primary-hover"]
			until true
		end
		repeat
			if variant == "primary" then
				return COLORS.primary
			end
			if variant == "success" then
				return COLORS.success
			end
			if variant == "warning" then
				return COLORS.warning
			end
			if variant == "error" then
				return COLORS.error
			end
			if variant == "ghost" then
				return "transparent"
			end
			if variant == "outline" then
				return "transparent"
			end
			return COLORS.primary
		until true
	end
	local getTextColor = function()
		if props.disabled then
			return COLORS.text.muted
		end
		local variant = props.variant or "primary"
		repeat
			local _fallthrough = false
			if variant == "ghost" then
				_fallthrough = true
			end
			if _fallthrough or variant == "outline" then
				return if hovered then COLORS.primary else COLORS.text.main
			end
			return COLORS.text.inverse
		until true
	end
	local getBorderColor = function()
		if props.disabled then
			return COLORS.border.l2
		end
		local variant = props.variant or "primary"
		if variant == "outline" then
			return if hovered then COLORS.primary else COLORS.border.l3
		end
		return "transparent"
	end
	local getBackgroundTransparency = function()
		local variant = props.variant or "primary"
		if variant == "ghost" then
			return 1
		end
		if variant == "outline" then
			return if hovered then 0.95 else 1
		end
		return 0
	end
	local size = props.size or UDim2.new(0, 140, 0, 44)
	return React.createElement("frame", {
		Size = size,
		Position = UDim2.new(0, 0, 0, 0),
		LayoutOrder = props.LayoutOrder,
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, React.createElement("frame", {
		Size = UDim2.new(1, 4, 1, 4),
		Position = UDim2.new(0, 2, 0, 2),
		BackgroundColor3 = Color3.fromRGB(0, 0, 0),
		BackgroundTransparency = 0.85,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 10),
	})), React.createElement("textbutton", {
		Text = props.text,
		TextColor3 = QuickUtils.safeColor(getTextColor(), COLORS.text.main),
		BackgroundColor3 = QuickUtils.safeColor(getBackgroundColor(), COLORS.bg.base),
		BackgroundTransparency = getBackgroundTransparency(),
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		Font = Enum.Font.GothamBold,
		TextSize = 14,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		Event = {
			Activated = function()
				if not props.disabled and not props.loading then
					props.onClick()
				end
			end,
			MouseEnter = function()
				if not props.disabled and not props.loading then
					setHovered(true)
				end
			end,
			MouseLeave = function()
				setHovered(false)
				setPressed(false)
			end,
			MouseButton1Down = function()
				if not props.disabled and not props.loading then
					setPressed(true)
				end
			end,
			MouseButton1Up = function()
				setPressed(false)
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}), React.createElement("uistroke", {
		Color = QuickUtils.safeColor(getBorderColor(), COLORS.border.base),
		Thickness = if props.variant == "outline" then 2 else 0,
		Transparency = if props.variant == "outline" then 0 else 1,
	}), React.createElement("frame", {
		Size = UDim2.new(1, 0, 0.5, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromRGB(255, 255, 255),
		BackgroundTransparency = 0.9,
		BorderSizePixel = 0,
		ZIndex = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}))))
end
return {
	Button = Button,
}
