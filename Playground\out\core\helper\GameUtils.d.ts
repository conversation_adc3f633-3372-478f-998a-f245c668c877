import { MathUtils } from "./MathUtils";
import { StringUtils } from "./StringUtils";
import { TimeUtils } from "./TimeUtils";
import { TableUtils } from "./TableUtils";
import { PositionHelper } from "./PositionHelper";
export { MathUtils, StringUtils, TimeUtils, TableUtils, PositionHelper };
/**
 * Convenience re-exports for common operations
 */
export declare const Utils: {
    Math: typeof MathUtils;
    String: typeof StringUtils;
    Time: typeof TimeUtils;
    Table: typeof TableUtils;
    Position: typeof PositionHelper;
};
/**
 * Quick utility functions for common tasks
 */
export declare const QuickUtils: {
    /**
     * Format any number for display
     */
    formatNumber: (num: number) => string;
    /**
     * Format time duration
     */
    formatTime: (seconds: number) => string;
    /**
     * Random between min and max
     */
    random: (min: number, max: number) => number;
    /**
     * Random integer between min and max (inclusive)
     */
    randomInt: (min: number, max: number) => number;
    /**
     * Clamp value between min and max
     */
    clamp: (value: number, min: number, max: number) => number;
    /**
     * Linear interpolation
     */
    lerp: (a: number, b: number, t: number) => number;
    /**
     * Check if point is in range of another point
     */
    inRange: (pos1: Vector3, pos2: Vector3, range: number) => boolean;
    /**
     * Get distance between two points
     */
    distance: (pos1: Vector3, pos2: Vector3) => number;
    /**
     * Round number to decimal places
     */
    round: (value: number, decimals?: number) => number;
    /**
     * Deep copy any object
     */
    copy: <T>(obj: T) => T;
    /**
     * Wait for a condition
     */
    waitFor: (condition: () => boolean, timeout?: number) => Promise<boolean>;
    /**
     * Generate a unique ID
     */
    generateId: () => string;
    /**
     * Check if string is empty or whitespace
     */
    isEmpty: (str: string) => boolean;
    /**
     * Capitalize first letter
     */
    capitalize: (str: string) => string;
    /**
     * Get current game time
     */
    gameTime: () => number;
    /**
     * Format player name for display
     */
    playerName: (player: Player) => string;
    /**
     * Safe color conversion that handles transparent and rgba values
     */
    safeColor: (colorValue: string, fallback?: string) => Color3;
    /**
     * Check if value exists and is not nil
     */
    exists: (value: unknown) => value is {} | null;
    /**
     * Get value or default
     */
    default: <T>(value: T | undefined, defaultValue: T) => T;
};
