/**
 * String manipulation utilities for game development
 */
export class StringUtils {
	/**
	 * Format time in seconds to MM:SS format
	 */
	static formatTime(seconds: number): string {
		const minutes = math.floor(seconds / 60);
		const remainingSeconds = math.floor(seconds % 60);
		return `${string.format("%02d", minutes)}:${string.format("%02d", remainingSeconds)}`;
	}

	/**
	 * Format time in seconds to HH:MM:SS format
	 */
	static formatTimeHours(seconds: number): string {
		const hours = math.floor(seconds / 3600);
		const minutes = math.floor((seconds % 3600) / 60);
		const remainingSeconds = math.floor(seconds % 60);
		return `${string.format("%02d", hours)}:${string.format("%02d", minutes)}:${string.format("%02d", remainingSeconds)}`;
	}

	/**
	 * Format large numbers with K, M, B suffixes
	 */
	static formatNumber(num: number): string {
		if (num >= 1000000000) {
			return string.format("%.1fB", num / 1000000000);
		} else if (num >= 1000000) {
			return string.format("%.1fM", num / 1000000);
		} else if (num >= 1000) {
			return string.format("%.1fK", num / 1000);
		} else {
			return tostring(num);
		}
	}

	/**
	 * Format currency with proper commas
	 */
	static formatCurrency(amount: number, currency = "$"): string {
		const formatted = this.addCommas(amount);
		return `${currency}${formatted}`;
	}

	/**
	 * Add commas to large numbers
	 */
	static addCommas(num: number): string {
		const str = tostring(math.floor(num));
		const parts: string[] = [];

		for (let i = str.size(); i > 0; i -= 3) {
			const start = math.max(1, i - 2);
			const part = str.sub(start, i);
			parts.unshift(part);
		}

		return parts.join(",");
	}

	/**
	 * Capitalize first letter of each word
	 */
	static titleCase(str: string): string {
		return str.gsub("%w+", (word) => word.sub(1, 1).upper() + word.sub(2).lower())[0];
	}

	/**
	 * Convert string to camelCase
	 */
	static toCamelCase(str: string): string {
		return str.gsub("[-_%s]+(.)", (letter) => letter.upper())[0];
	}

	/**
	 * Convert camelCase to snake_case
	 */
	static toSnakeCase(str: string): string {
		return str.gsub("([a-z])([A-Z])", "%1_%2")[0].lower();
	}

	/**
	 * Convert string to kebab-case
	 */
	static toKebabCase(str: string): string {
		return str.gsub("([a-z])([A-Z])", "%1-%2")[0].lower();
	}

	/**
	 * Truncate string with ellipsis
	 */
	static truncate(str: string, maxLength: number, suffix = "..."): string {
		if (str.size() <= maxLength) {
			return str;
		}
		return str.sub(1, maxLength - suffix.size()) + suffix;
	}

	/**
	 * Pad string to specified length
	 */
	static pad(str: string, length: number, char = " ", left = true): string {
		const padding = char.rep(math.max(0, length - str.size()));
		return left ? padding + str : str + padding;
	}

	/**
	 * Remove all whitespace from string
	 */
	static removeWhitespace(str: string): string {
		return str.gsub("%s+", "")[0];
	}

	/**
	 * Count occurrences of substring
	 */
	static countOccurrences(str: string, pattern: string): number {
		let count = 0;
		let pos = 1;

		while (true) {
			const found = str.find(pattern, pos);
			if (!found[0]) break;

			count++;
			pos = found[0] + 1;
		}

		return count;
	}

	/**
	 * Generate random string of specified length
	 */
	static randomString(
		length: number,
		charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
	): string {
		let result = "";
		for (let i = 0; i < length; i++) {
			const randomIndex = math.random(1, charset.size());
			result += charset.sub(randomIndex, randomIndex);
		}
		return result;
	}

	/**
	 * Generate UUID-like string
	 */
	static generateId(): string {
		const chars = "0123456789abcdef";
		let result = "";

		for (let i = 0; i < 32; i++) {
			if (i === 8 || i === 12 || i === 16 || i === 20) {
				result += "-";
			}
			const randomIndex = math.random(1, chars.size());
			result += chars.sub(randomIndex, randomIndex);
		}

		return result;
	}

	/**
	 * Convert RGB to hex color
	 */
	static rgbToHex(r: number, g: number, b: number): string {
		const toHex = (n: number) => {
			const hex = string.format("%x", math.clamp(math.floor(n), 0, 255));
			return hex.size() === 1 ? "0" + hex : hex;
		};

		return "#" + toHex(r) + toHex(g) + toHex(b);
	}

	/**
	 * Convert Color3 to hex string
	 */
	static color3ToHex(color: Color3): string {
		return this.rgbToHex(color.R * 255, color.G * 255, color.B * 255);
	}

	/**
	 * Check if string is valid email format
	 */
	static isValidEmail(email: string): boolean {
		const pattern = "^[%w%._%+%-]+@[%w%.%-]+%.%w+$";
		return email.match(pattern)[0] !== undefined;
	}

	/**
	 * Escape special characters for pattern matching
	 */
	static escapePattern(str: string): string {
		return str.gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1")[0];
	}

	/**
	 * Split string by delimiter
	 */
	static split(str: string, delimiter: string): string[] {
		const parts: string[] = [];
		const pattern = `([^${this.escapePattern(delimiter)}]+)`;

		for (const [match] of str.gmatch(pattern)) {
			if (typeOf(match) === "string") {
				parts.push(match as string);
			}
		}

		return parts;
	}

	/**
	 * Join array of strings with delimiter
	 */
	static join(parts: string[], delimiter: string): string {
		if (parts.size() === 0) return "";
		if (parts.size() === 1) return parts[0];

		let result = parts[0];
		for (let i = 1; i < parts.size(); i++) {
			result += delimiter + parts[i];
		}

		return result;
	}

	/**
	 * Check if string starts with prefix
	 */
	static startsWith(str: string, prefix: string): boolean {
		return str.sub(1, prefix.size()) === prefix;
	}

	/**
	 * Check if string ends with suffix
	 */
	static endsWith(str: string, suffix: string): boolean {
		return str.sub(-suffix.size()) === suffix;
	}

	/**
	 * Format player name with display formatting
	 */
	static formatPlayerName(player: Player): string {
		const displayName = player.DisplayName;
		const username = player.Name;

		if (displayName === username) {
			return username;
		} else {
			return `${displayName} (@${username})`;
		}
	}

	/**
	 * Convert markdown-style formatting to rich text
	 */
	static markdownToRichText(text: string): string {
		// Bold: **text** -> <b>text</b>
		text = text.gsub("%*%*(.-)%*%*", "<b>%1</b>")[0];

		// Italic: *text* -> <i>text</i>
		text = text.gsub("%*(.-)%*", "<i>%1</i>")[0];

		// Code: `text` -> <font face="SourceCodePro">text</font>
		text = text.gsub("`(.-)`", '<font family="SourceCodePro">%1</font>')[0];

		return text;
	}

	/**
	 * Generate initials from name
	 */
	static getInitials(name: string, maxInitials = 2): string {
		const words = this.split(name, " ");
		let initials = "";

		for (let i = 0; i < math.min(words.size(), maxInitials); i++) {
			const word = words[i];
			if (word.size() > 0) {
				initials += word.sub(1, 1).upper();
			}
		}

		return initials;
	}

	/**
	 * Format bytes to human readable format
	 */
	static formatBytes(bytes: number): string {
		const units = ["B", "KB", "MB", "GB"];
		let value = bytes;
		let unitIndex = 0;

		while (value >= 1024 && unitIndex < units.size() - 1) {
			value /= 1024;
			unitIndex++;
		}

		if (unitIndex === 0) {
			return `${value} ${units[unitIndex]}`;
		} else {
			return `${string.format("%.1f", value)} ${units[unitIndex]}`;
		}
	}
}
