import * as React from "@rbxts/react";
import { COLORS } from "../../design";

interface ProgressBarProps {
	progress: number; // 0 to 1
	barColor?: Color3;
	backgroundColor?: Color3;
	size?: UDim2;
	cornerRadius?: UDim;
	borderSize?: number;
	borderColor?: Color3;
	layoutOrder?: number;
	position?: UDim2;
	anchorPoint?: Vector2;
}

export function ProgressBar({
	progress,
	barColor = Color3.fromHex(COLORS.primary),
	backgroundColor = Color3.fromHex(COLORS.bg.secondary),
	size = new UDim2(1, 0, 0, 20),
	cornerRadius = new UDim(0, 6),
	borderSize = 1,
	borderColor = Color3.fromHex(COLORS.border.l2),
	layoutOrder,
	position,
	anchorPoint,
}: ProgressBarProps): React.ReactElement {
	// Clamp progress between 0 and 1
	const clampedProgress = math.max(0, math.min(1, progress));

	return (
		<frame
			Size={size}
			Position={position}
			AnchorPoint={anchorPoint}
			BackgroundColor3={backgroundColor}
			BackgroundTransparency={0.1} // Slight transparency for modern look
			BorderSizePixel={0}
			LayoutOrder={layoutOrder}
		>
			{/* Modern corner radius for background */}
			<uicorner CornerRadius={cornerRadius} />

			{/* Subtle border stroke */}
			<uistroke Color={borderColor} Thickness={borderSize} Transparency={0.5} />

			{/* Progress fill with enhanced styling */}
			<frame
				Size={new UDim2(clampedProgress, 0, 1, 0)}
				Position={new UDim2(0, 0, 0, 0)}
				BackgroundColor3={barColor}
				BorderSizePixel={0}
			>
				{/* Corner radius for progress fill */}
				<uicorner CornerRadius={cornerRadius} />

				{/* Subtle highlight for depth */}
				<frame
					Size={new UDim2(1, 0, 0.4, 0)}
					Position={new UDim2(0, 0, 0, 0)}
					BackgroundColor3={Color3.fromRGB(255, 255, 255)}
					BackgroundTransparency={0.85}
					BorderSizePixel={0}
				>
					<uicorner CornerRadius={cornerRadius} />
				</frame>
			</frame>
		</frame>
	);
}
