-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local EntityManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager").EntityManager
local EntityType = TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "enums", "EntityType").EntityType
local AIController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "AIController").AIController
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local RunService = _services.RunService
local Players = _services.Players
local Workspace = _services.Workspace
local playSound = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper").playSound
local createParticleExplosion = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "ParticleHelper").createParticleExplosion
local createImpactFlash = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "VisualEffectUtils").createImpactFlash
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local ElementalBattleGame
do
	ElementalBattleGame = setmetatable({}, {
		__tostring = function()
			return "ElementalBattleGame"
		end,
	})
	ElementalBattleGame.__index = ElementalBattleGame
	function ElementalBattleGame.new(...)
		local self = setmetatable({}, ElementalBattleGame)
		return self:constructor(...) or self
	end
	function ElementalBattleGame:constructor(config)
		self.activeEnemies = {}
		self.startTime = 0
		self.waveStartTime = 0
		self.player = Players.LocalPlayer
		self.entityManager = EntityManager:getInstance()
		self.aiController = AIController:getInstance()
		-- Default configuration
		local _object = {
			arenaSize = 60,
			arenaCenter = Vector3.new(0, 5, 0),
			basePlayerHealth = 100,
			baseEnemiesPerWave = 3,
			waveMultiplier = 1.5,
			enemyHealthMultiplier = 1.2,
			timeBetweenWaves = 10,
			scorePerEnemyKill = 100,
			experiencePerEnemyKill = 25,
			experiencePerLevel = 100,
		}
		if config then
			for _k, _v in config do
				_object[_k] = _v
			end
		end
		self.config = _object
		self.gameState = {
			playerHealth = self.config.basePlayerHealth,
			maxPlayerHealth = self.config.basePlayerHealth,
			currentWave = 0,
			enemiesInWave = 0,
			enemiesRemaining = 0,
			score = 0,
			isGameActive = false,
			gamePhase = "waiting",
			timeUntilNextWave = 0,
			abilitiesUnlocked = { "QUAKE_PUNCH", "FIRE_FIST" },
			playerLevel = 1,
			experience = 0,
			experienceToNextLevel = self.config.experiencePerLevel,
		}
		print(`⚔️ [ElementalBattle] Game initialized with config:`, self.config)
	end
	function ElementalBattleGame:getName()
		return "Elemental Battle Arena"
	end
	function ElementalBattleGame:getGameState()
		local _object = table.clone(self.gameState)
		setmetatable(_object, nil)
		return _object
	end
	ElementalBattleGame.start = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			print("⚔️ [ElementalBattle] Starting game...")
			-- Reset game state
			self.startTime = tick()
			local _object = table.clone(self.gameState)
			setmetatable(_object, nil)
			_object.playerHealth = self.config.basePlayerHealth
			_object.currentWave = 0
			_object.enemiesInWave = 0
			_object.enemiesRemaining = 0
			_object.score = 0
			_object.isGameActive = true
			_object.gamePhase = "playing"
			_object.timeUntilNextWave = 0
			self.gameState = _object
			-- Clear any existing entities
			TS.await(self:cleanup())
			-- Create arena environment
			self:createArena()
			-- Start first wave
			self:startNextWave()
			-- Start game loop
			self:startGameLoop()
			print("⚔️ [ElementalBattle] Game started successfully!")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			local gameError = createError(`Failed to start Elemental Battle: {error}`, "GAME_START_FAILED")
			warn(`❌ [ElementalBattle] {gameError.message}`)
			return TS.TRY_RETURN, { Result:err(gameError) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	ElementalBattleGame.stop = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			print("⚔️ [ElementalBattle] Stopping game...")
			self.gameState.isGameActive = false
			self.gameState.gamePhase = "ended"
			-- Stop game loop
			if self.gameLoop then
				self.gameLoop:Disconnect()
				self.gameLoop = nil
			end
			if self.waveTimer then
				self.waveTimer:Disconnect()
				self.waveTimer = nil
			end
			-- Cleanup entities
			TS.await(self:cleanup())
			print("⚔️ [ElementalBattle] Game stopped successfully!")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			local gameError = createError(`Failed to stop Elemental Battle: {error}`, "GAME_STOP_FAILED")
			warn(`❌ [ElementalBattle] {gameError.message}`)
			return TS.TRY_RETURN, { Result:err(gameError) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function ElementalBattleGame:isActive()
		return self.gameState.isGameActive
	end
	function ElementalBattleGame:createArena()
		-- Create arena floor with visual effects
		local arena = Instance.new("Part")
		arena.Name = "ElementalArena"
		arena.Size = Vector3.new(self.config.arenaSize * 2, 1, self.config.arenaSize * 2)
		arena.Position = self.config.arenaCenter
		arena.Anchored = true
		arena.Material = Enum.Material.Neon
		arena.BrickColor = BrickColor.new("Really black")
		arena.Parent = Workspace
		-- Add glowing effect
		local pointLight = Instance.new("PointLight")
		pointLight.Brightness = 2
		pointLight.Color = Color3.fromRGB(100, 200, 255)
		pointLight.Range = self.config.arenaSize
		pointLight.Parent = arena
		-- Create arena walls (invisible barriers)
		for i = 0, 3 do
			local wall = Instance.new("Part")
			wall.Name = `ArenaWall{i}`
			wall.Size = Vector3.new(2, 20, self.config.arenaSize * 2 + 4)
			wall.Transparency = 1
			wall.CanCollide = true
			wall.Anchored = true
			wall.Parent = Workspace
			-- Position walls around arena
			repeat
				if i == 0 then
					local _arenaCenter = self.config.arenaCenter
					local _vector3 = Vector3.new(self.config.arenaSize + 2, 10, 0)
					wall.Position = _arenaCenter + _vector3
					break
				end
				if i == 1 then
					local _arenaCenter = self.config.arenaCenter
					local _vector3 = Vector3.new(-self.config.arenaSize - 2, 10, 0)
					wall.Position = _arenaCenter + _vector3
					break
				end
				if i == 2 then
					wall.Size = Vector3.new(self.config.arenaSize * 2 + 4, 20, 2)
					local _arenaCenter = self.config.arenaCenter
					local _vector3 = Vector3.new(0, 10, self.config.arenaSize + 2)
					wall.Position = _arenaCenter + _vector3
					break
				end
				if i == 3 then
					wall.Size = Vector3.new(self.config.arenaSize * 2 + 4, 20, 2)
					local _arenaCenter = self.config.arenaCenter
					local _vector3 = Vector3.new(0, 10, -self.config.arenaSize - 2)
					wall.Position = _arenaCenter + _vector3
					break
				end
			until true
		end
		print("⚔️ [ElementalBattle] Arena created successfully!")
	end
	function ElementalBattleGame:startNextWave()
		self.gameState.currentWave += 1
		self.gameState.enemiesInWave = math.floor(self.config.baseEnemiesPerWave * self.config.waveMultiplier ^ (self.gameState.currentWave - 1))
		self.gameState.enemiesRemaining = self.gameState.enemiesInWave
		self.gameState.gamePhase = "playing"
		self.waveStartTime = tick()
		print(`⚔️ [ElementalBattle] Starting Wave {self.gameState.currentWave} with {self.gameState.enemiesInWave} enemies`)
		-- Spawn enemies for this wave
		self:spawnWaveEnemies()
		-- Visual effect for wave start
		createParticleExplosion(self.config.arenaCenter, 50, Color3.fromRGB(255, 100, 100), { 10, 30 }, { 0.5, 1.5 })
		-- Play wave start sound
		playSound("rbxasset://sounds/electronicpingshort.wav", 0.5)
	end
	function ElementalBattleGame:spawnWaveEnemies()
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < self.gameState.enemiesInWave) then
					break
				end
				task.delay(i * 0.5, function()
					-- Stagger enemy spawning
					self:spawnEnemy()
				end)
			end
		end
	end
	function ElementalBattleGame:spawnEnemy()
		-- Random spawn position around arena edge
		local angle = math.random() * math.pi * 2
		local spawnRadius = self.config.arenaSize * 0.8
		local _arenaCenter = self.config.arenaCenter
		local _vector3 = Vector3.new(math.cos(angle) * spawnRadius, 2, math.sin(angle) * spawnRadius)
		local spawnPosition = _arenaCenter + _vector3
		-- Determine enemy type based on wave
		local enemyType = "basic"
		if self.gameState.currentWave >= 3 then
			local typeRoll = math.random()
			if typeRoll < 0.3 then
				enemyType = "fast"
			elseif typeRoll < 0.6 then
				enemyType = "heavy"
			elseif typeRoll < 0.8 then
				enemyType = "elemental"
			end
		end
		-- Spawn entity
		local entity = self.entityManager:spawnEntity({
			type = EntityType.NPC,
			position = spawnPosition,
			data = {
				name = `Enemy_{enemyType}_{self.gameState.currentWave}`,
				aiEnabled = true,
				health = 50 * self.config.enemyHealthMultiplier ^ (self.gameState.currentWave - 1),
			},
		})
		local entityId = entity.id
		local enemy = {
			entityId = entityId,
			health = 50 * self.config.enemyHealthMultiplier ^ (self.gameState.currentWave - 1),
			maxHealth = 50 * self.config.enemyHealthMultiplier ^ (self.gameState.currentWave - 1),
			enemyType = enemyType,
			element = if enemyType == "elemental" then (({ "fire", "ice", "earth" })[math.random(0, 2) + 1]) else nil,
			position = spawnPosition,
			isAlive = true,
		}
		self.activeEnemies[entityId] = enemy
		-- Configure AI for this enemy
		self:configureEnemyAI(entityId, enemyType)
		-- Visual spawn effect
		createImpactFlash(spawnPosition, 4, 0.5)
		print(`⚔️ [ElementalBattle] Spawned {enemyType} enemy with {enemy.health} health`)
	end
	function ElementalBattleGame:configureEnemyAI(entityId, enemyType)
		local playerCharacter = self.player.Character
		if not playerCharacter then
			return nil
		end
		-- Register AI agent with basic config
		local aiAgent = self.aiController:registerAI(entityId, {
			detectionRange = 50,
			moveSpeed = if enemyType == "fast" then 20 elseif enemyType == "heavy" then 8 else 12,
			attackRange = 5,
		})
		print(`⚔️ [ElementalBattle] AI configured for {enemyType} enemy: {entityId}`)
	end
	function ElementalBattleGame:startGameLoop()
		self.gameLoop = RunService.Heartbeat:Connect(function()
			if not self.gameState.isGameActive then
				return nil
			end
			-- Update enemy states
			self:updateEnemies()
			-- Check wave completion
			if self.gameState.enemiesRemaining <= 0 and self.gameState.gamePhase == "playing" then
				self:onWaveCompleted()
			end
			-- Update between wave timer
			if self.gameState.gamePhase == "betweenWaves" then
				self.gameState.timeUntilNextWave = math.max(0, self.config.timeBetweenWaves - (tick() - self.waveStartTime))
				if self.gameState.timeUntilNextWave <= 0 then
					self:startNextWave()
				end
			end
		end)
	end
	function ElementalBattleGame:updateEnemies()
		local _exp = self.activeEnemies
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(enemy, entityId)
			if not enemy.isAlive then
				return nil
			end
			-- Check if enemy entity still exists
			local entityData = self.entityManager:getEntity(entityId)
			if not entityData then
				-- Enemy was destroyed, count as killed
				self:onEnemyKilled(entityId)
				return nil
			end
			-- Update enemy position if entity exists
			if entityData and entityData.instance then
				enemy.position = PositionHelper:getPosition(entityData.instance)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
	end
	function ElementalBattleGame:onEnemyKilled(entityId)
		local _activeEnemies = self.activeEnemies
		local _entityId = entityId
		local enemy = _activeEnemies[_entityId]
		if not enemy or not enemy.isAlive then
			return nil
		end
		enemy.isAlive = false
		self.gameState.enemiesRemaining -= 1
		self.gameState.score += self.config.scorePerEnemyKill
		self.gameState.experience += self.config.experiencePerEnemyKill
		-- Check for level up
		if self.gameState.experience >= self.gameState.experienceToNextLevel then
			self:levelUp()
		end
		-- Visual effect for enemy death
		createParticleExplosion(enemy.position, 30, Color3.fromRGB(255, 255, 0), { 5, 20 }, { 0.3, 0.8 })
		-- Play kill sound
		playSound("rbxasset://sounds/impact_generic_large_01.mp3", 0.3)
		-- Remove from active enemies
		local _activeEnemies_1 = self.activeEnemies
		local _entityId_1 = entityId
		_activeEnemies_1[_entityId_1] = nil
		print(`⚔️ [ElementalBattle] Enemy killed! Score: {self.gameState.score}, XP: {self.gameState.experience}`)
	end
	function ElementalBattleGame:onWaveCompleted()
		self.gameState.gamePhase = "betweenWaves"
		self.waveStartTime = tick()
		self.gameState.timeUntilNextWave = self.config.timeBetweenWaves
		-- Bonus score for completing wave
		local waveBonus = self.gameState.currentWave * 50
		self.gameState.score += waveBonus
		print(`⚔️ [ElementalBattle] Wave {self.gameState.currentWave} completed! Bonus: {waveBonus} points`)
		-- Celebration effect
		local _arenaCenter = self.config.arenaCenter
		local _vector3 = Vector3.new(0, 5, 0)
		createParticleExplosion(_arenaCenter + _vector3, 100, Color3.fromRGB(0, 255, 0), { 10, 35 }, { 0.5, 1.2 })
	end
	function ElementalBattleGame:levelUp()
		self.gameState.playerLevel += 1
		self.gameState.experience -= self.gameState.experienceToNextLevel
		self.gameState.experienceToNextLevel = self.config.experiencePerLevel * self.gameState.playerLevel
		-- Increase player health
		self.gameState.maxPlayerHealth += 20
		self.gameState.playerHealth = self.gameState.maxPlayerHealth
		-- Unlock new abilities
		self:unlockAbilities()
		print(`⚔️ [ElementalBattle] Level Up! Now level {self.gameState.playerLevel}`)
		-- Level up effect
		if self.player.Character then
			local characterPosition = PositionHelper:getPosition(self.player.Character)
			createParticleExplosion(characterPosition, 80, Color3.fromRGB(255, 215, 0), { 10, 30 }, { 0.4, 1.0 })
		end
	end
	function ElementalBattleGame:unlockAbilities()
		local allAbilities = { "QUAKE_PUNCH", "FIRE_FIST", "ICE_AGE", "THREE_SWORD_STYLE", "ROOM" }
		if self.gameState.playerLevel >= 3 and not (table.find(self.gameState.abilitiesUnlocked, "ICE_AGE") ~= nil) then
			local _exp = self.gameState.abilitiesUnlocked
			table.insert(_exp, "ICE_AGE")
			print("❄️ [ElementalBattle] Unlocked Ice Age ability!")
		end
		if self.gameState.playerLevel >= 5 and not (table.find(self.gameState.abilitiesUnlocked, "THREE_SWORD_STYLE") ~= nil) then
			local _exp = self.gameState.abilitiesUnlocked
			table.insert(_exp, "THREE_SWORD_STYLE")
			print("⚔️ [ElementalBattle] Unlocked Three Sword Style ability!")
		end
		if self.gameState.playerLevel >= 7 and not (table.find(self.gameState.abilitiesUnlocked, "ROOM") ~= nil) then
			local _exp = self.gameState.abilitiesUnlocked
			table.insert(_exp, "ROOM")
			print("🔵 [ElementalBattle] Unlocked Room ability!")
		end
	end
	function ElementalBattleGame:damagePlayer(damage)
		self.gameState.playerHealth = math.max(0, self.gameState.playerHealth - damage)
		if self.gameState.playerHealth <= 0 then
			self:onPlayerDeath()
		end
		print(`⚔️ [ElementalBattle] Player took {damage} damage. Health: {self.gameState.playerHealth}/{self.gameState.maxPlayerHealth}`)
	end
	function ElementalBattleGame:onPlayerDeath()
		print("💀 [ElementalBattle] Player died! Game Over.")
		self:stop()
	end
	ElementalBattleGame.cleanup = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			-- Remove all active enemies
			local _exp = self.activeEnemies
			-- ▼ ReadonlyMap.forEach ▼
			local _callback = function(enemy, entityId)
				self.entityManager:destroyEntity(entityId)
				self.aiController:unregisterAI(entityId)
			end
			for _k, _v in _exp do
				_callback(_v, _k, _exp)
			end
			-- ▲ ReadonlyMap.forEach ▲
			table.clear(self.activeEnemies)
			-- Remove arena objects
			local arena = Workspace:FindFirstChild("ElementalArena")
			if arena then
				arena:Destroy()
			end
			for i = 0, 3 do
				local wall = Workspace:FindFirstChild(`ArenaWall{i}`)
				if wall then
					wall:Destroy()
				end
			end
			print("⚔️ [ElementalBattle] Cleanup completed")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			local cleanupError = createError(`Failed to cleanup Elemental Battle: {error}`, "CLEANUP_FAILED")
			warn(`❌ [ElementalBattle] {cleanupError.message}`)
			return TS.TRY_RETURN, { Result:err(cleanupError) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
end
return {
	ElementalBattleGame = ElementalBattleGame,
}
