-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Button = _core.Button
local VerticalFrame = _core.VerticalFrame
local ClientState = _core.ClientState
local useUIState = _core.useUIState
local WorldTestingPanel = TS.import(script, script.Parent, "WorldTestingPanel").WorldTestingPanel
local DebugPanel = TS.import(script, script.Parent, "DebugPanel").DebugPanel
local CollectorArenaUI = TS.import(script, script.Parent, "CollectorArenaUI").CollectorArenaUI
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function BottomLeftGrid(_props)
	-- Use Core state management instead of local state - with safety checks
	local worldTestingState = useUIState("worldTestingPanel")
	local debugState = useUIState("debugPanel")
	local debugPanelOpen, setDebugPanelOpen = React.useState(false)
	local gameUIOpen, setGameUIOpen = React.useState(false)
	-- Get responsive manager for dynamic positioning (memoized) - with safety check
	local responsiveManager = React.useMemo(function()
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { ResponsiveManager:getInstance() }
		end, function(error)
			warn(`❌ Failed to get ResponsiveManager: {error}`)
			return TS.TRY_RETURN, { nil }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end, {})
	-- Memoize expensive calculations - with safety checks
	local responsiveSettings = React.useMemo(function()
		if not responsiveManager then
			-- Fallback values if ResponsiveManager fails
			return {
				containerWidth = 120,
				containerHeight = 250,
				marginLeft = 16,
				marginBottom = 20,
			}
		end
		local _exitType, _returns = TS.try(function()
			local safeAreaInsets = responsiveManager:getSafeAreaInsets()
			local containerWidth = if responsiveManager:isMobile() then 100 else 120
			local containerHeight = if responsiveManager:isMobile() then 200 else 250
			local marginLeft = responsiveManager:getResponsiveMargin(16)
			local marginBottom = responsiveManager:getResponsiveMargin(20) + safeAreaInsets.bottom
			return TS.TRY_RETURN, { {
				containerWidth = containerWidth,
				containerHeight = containerHeight,
				marginLeft = marginLeft,
				marginBottom = marginBottom,
			} }
		end, function(error)
			warn(`❌ Failed to calculate responsive settings: {error}`)
			-- Fallback values
			return TS.TRY_RETURN, { {
				containerWidth = 120,
				containerHeight = 250,
				marginLeft = 16,
				marginBottom = 20,
			} }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end, { responsiveManager })
	-- Memoize button handlers with error handling
	local handleWorldClick = React.useCallback(function()
		TS.try(function()
			ClientState:updateUI("worldTestingPanel", {
				isOpen = true,
			})
		end, function(error)
			warn(`❌ Failed to update world testing panel state: {error}`)
		end)
	end, {})
	local handleDebugClick = React.useCallback(function()
		TS.try(function()
			setDebugPanelOpen(true)
		end, function(error)
			warn(`❌ Failed to open debug panel: {error}`)
		end)
	end, {})
	local handleGameClick = React.useCallback(function()
		TS.try(function()
			setGameUIOpen(true)
		end, function(error)
			warn(`❌ Failed to open game UI: {error}`)
		end)
	end, {})
	-- Memoize frame props
	local frameProps = React.useMemo(function()
		return {
			backgroundTransparency = 1,
			size = UDim2.new(0, responsiveSettings.containerWidth, 0, responsiveSettings.containerHeight),
			position = UDim2.new(0, responsiveSettings.marginLeft, 1, -responsiveSettings.marginBottom),
			anchorPoint = Vector2.new(0, 1),
			spacing = 8,
			padding = 0,
			responsive = true,
			responsiveMargin = true,
		}
	end, { responsiveSettings })
	local _attributes = table.clone(frameProps)
	setmetatable(_attributes, nil)
	return React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
	}, React.createElement(VerticalFrame, _attributes, React.createElement(Button, {
		text = "🌍 World",
		variant = "primary",
		onClick = handleWorldClick,
		LayoutOrder = 5,
		responsive = true,
	}), React.createElement(Button, {
		text = "🏟️ Game",
		variant = "primary",
		onClick = handleGameClick,
		LayoutOrder = 6,
		responsive = true,
	}), React.createElement(Button, {
		text = "🔧 Debug",
		variant = "secondary",
		onClick = handleDebugClick,
		LayoutOrder = 7,
		responsive = true,
	})), React.createElement(WorldTestingPanel), React.createElement(CollectorArenaUI, {
		isOpen = gameUIOpen,
		onClose = function()
			return setGameUIOpen(false)
		end,
	}), React.createElement(DebugPanel, {
		isOpen = debugPanelOpen,
		onClose = function()
			return setDebugPanelOpen(false)
		end,
	}))
end
return {
	BottomLeftGrid = BottomLeftGrid,
}
