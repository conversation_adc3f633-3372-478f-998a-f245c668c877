export declare const COLORS: {
    text: {
        main: string;
        secondary: string;
        muted: string;
        inverse: string;
        accent: string;
    };
    bg: {
        base: string;
        secondary: string;
        surface: string;
        "surface-hover": string;
        "surface-pressed": string;
        avatar: string;
        badge: string;
        hover: string;
        modal: string;
        card: string;
        accent: string;
    };
    label: {
        text: string;
        focus: string;
        bg: string;
        border: string;
        muted: string;
        hover: string;
    };
    span: {
        default: string;
        muted: string;
        highlight: string;
        subtle: string;
        hover: string;
    };
    border: {
        base: string;
        l1: string;
        l2: string;
        l3: string;
        strong: string;
        focus: string;
        danger: string;
        success: string;
        accent: string;
    };
    primary: string;
    "primary-dark": string;
    "primary-light": string;
    "primary-hover": string;
    success: string;
    "success-dark": string;
    "success-light": string;
    "success-hover": string;
    warning: string;
    "warning-dark": string;
    "warning-light": string;
    "warning-hover": string;
    error: string;
    "error-dark": string;
    "error-light": string;
    "error-hover": string;
    info: string;
    "info-dark": string;
    "info-light": string;
    "info-hover": string;
    "progress-bg": string;
    "progress-fill": string;
    "account-active": string;
    "account-active-hover": string;
    badge: {
        bg: string;
        text: string;
        border: string;
        success: string;
        warning: string;
        error: string;
    };
    ring: {
        "focus-accent": string;
    };
    shadow: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
    };
    red: string;
    "red-hover": string;
    "red-light": string;
    blue: string;
    "blue-hover": string;
    "blue-light": string;
    green: string;
    "green-hover": string;
    "green-light": string;
    yellow: string;
    "yellow-hover": string;
    "yellow-light": string;
    purple: string;
    "purple-hover": string;
    "purple-light": string;
    pink: string;
    "pink-hover": string;
    "pink-light": string;
    gray: string;
    "gray-hover": string;
    "gray-light": string;
    "roblox-red": string;
    "roblox-blue": string;
    "roblox-green": string;
    "roblox-orange": string;
    "roblox-purple": string;
    "toast-success": string;
    "toast-warning": string;
    "toast-error": string;
    "toast-info": string;
    "status-overlay": string;
    "alert-overlay": string;
};
