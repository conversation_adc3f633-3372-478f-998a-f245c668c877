import { GameController } from "./GameModeManager";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/RobloxError";
import { EntityManager } from "../entities/EntityManager";
import { EntityType } from "../entities/enums/EntityType";
import { AIController } from "../ai/AIController";
import { RunService, Players, Workspace } from "@rbxts/services";
import { playSound } from "../effects/SoundHelper";
import { createParticleExplosion } from "../effects/ParticleHelper";
import { createImpactFlash } from "../effects/VisualEffectUtils";
import { PositionHelper } from "../helper/PositionHelper";

export interface ElementalBattleState {
	playerHealth: number;
	maxPlayerHealth: number;
	currentWave: number;
	enemiesInWave: number;
	enemiesRemaining: number;
	score: number;
	isGameActive: boolean;
	gamePhase: "waiting" | "playing" | "betweenWaves" | "ended";
	timeUntilNextWave: number;
	abilitiesUnlocked: string[];
	playerLevel: number;
	experience: number;
	experienceToNextLevel: number;
}

export interface ElementalBattleConfig {
	arenaSize: number; // studs radius
	arenaCenter: Vector3;
	basePlayerHealth: number;
	baseEnemiesPerWave: number;
	waveMultiplier: number; // enemies increase per wave
	enemyHealthMultiplier: number; // enemy health increase per wave
	timeBetweenWaves: number; // seconds
	scorePerEnemyKill: number;
	experiencePerEnemyKill: number;
	experiencePerLevel: number;
}

export interface Enemy {
	entityId: string;
	health: number;
	maxHealth: number;
	enemyType: "basic" | "fast" | "heavy" | "elemental";
	element?: "fire" | "ice" | "earth";
	position: Vector3;
	isAlive: boolean;
}

export class ElementalBattleGame implements GameController {
	private entityManager: EntityManager;
	private aiController: AIController;
	private gameLoop?: RBXScriptConnection;
	private waveTimer?: RBXScriptConnection;
	private config: ElementalBattleConfig;
	private activeEnemies = new Map<string, Enemy>();
	private player: Player;
	private startTime: number = 0;
	private waveStartTime: number = 0;

	// Game state
	private gameState: ElementalBattleState;

	constructor(config?: Partial<ElementalBattleConfig>) {
		this.player = Players.LocalPlayer;
		this.entityManager = EntityManager.getInstance();
		this.aiController = AIController.getInstance();

		// Default configuration
		this.config = {
			arenaSize: 60, // 60 stud radius
			arenaCenter: new Vector3(0, 5, 0),
			basePlayerHealth: 100,
			baseEnemiesPerWave: 3,
			waveMultiplier: 1.5,
			enemyHealthMultiplier: 1.2,
			timeBetweenWaves: 10, // 10 seconds between waves
			scorePerEnemyKill: 100,
			experiencePerEnemyKill: 25,
			experiencePerLevel: 100,
			...config,
		};

		this.gameState = {
			playerHealth: this.config.basePlayerHealth,
			maxPlayerHealth: this.config.basePlayerHealth,
			currentWave: 0,
			enemiesInWave: 0,
			enemiesRemaining: 0,
			score: 0,
			isGameActive: false,
			gamePhase: "waiting",
			timeUntilNextWave: 0,
			abilitiesUnlocked: ["QUAKE_PUNCH", "FIRE_FIST"], // Start with basic abilities
			playerLevel: 1,
			experience: 0,
			experienceToNextLevel: this.config.experiencePerLevel,
		};

		print(`⚔️ [ElementalBattle] Game initialized with config:`, this.config);
	}

	public getName(): string {
		return "Elemental Battle Arena";
	}

	public getGameState(): ElementalBattleState {
		return { ...this.gameState }; // Return a copy
	}

	public async start(): Promise<Result<void, Error>> {
		try {
			print("⚔️ [ElementalBattle] Starting game...");

			// Reset game state
			this.startTime = tick();
			this.gameState = {
				...this.gameState,
				playerHealth: this.config.basePlayerHealth,
				currentWave: 0,
				enemiesInWave: 0,
				enemiesRemaining: 0,
				score: 0,
				isGameActive: true,
				gamePhase: "playing",
				timeUntilNextWave: 0,
			};

			// Clear any existing entities
			await this.cleanup();

			// Create arena environment
			this.createArena();

			// Start first wave
			this.startNextWave();

			// Start game loop
			this.startGameLoop();

			print("⚔️ [ElementalBattle] Game started successfully!");
			return Result.ok(undefined);
		} catch (error) {
			const gameError = createError(`Failed to start Elemental Battle: ${error}`, "GAME_START_FAILED");
			warn(`❌ [ElementalBattle] ${gameError.message}`);
			return Result.err(gameError);
		}
	}

	public async stop(): Promise<Result<void, Error>> {
		try {
			print("⚔️ [ElementalBattle] Stopping game...");

			this.gameState.isGameActive = false;
			this.gameState.gamePhase = "ended";

			// Stop game loop
			if (this.gameLoop) {
				this.gameLoop.Disconnect();
				this.gameLoop = undefined;
			}

			if (this.waveTimer) {
				this.waveTimer.Disconnect();
				this.waveTimer = undefined;
			}

			// Cleanup entities
			await this.cleanup();

			print("⚔️ [ElementalBattle] Game stopped successfully!");
			return Result.ok(undefined);
		} catch (error) {
			const gameError = createError(`Failed to stop Elemental Battle: ${error}`, "GAME_STOP_FAILED");
			warn(`❌ [ElementalBattle] ${gameError.message}`);
			return Result.err(gameError);
		}
	}

	public isActive(): boolean {
		return this.gameState.isGameActive;
	}

	private createArena(): void {
		// Create arena floor with visual effects
		const arena = new Instance("Part");
		arena.Name = "ElementalArena";
		arena.Size = new Vector3(this.config.arenaSize * 2, 1, this.config.arenaSize * 2);
		arena.Position = this.config.arenaCenter;
		arena.Anchored = true;
		arena.Material = Enum.Material.Neon;
		arena.BrickColor = new BrickColor("Really black");
		arena.Parent = Workspace;

		// Add glowing effect
		const pointLight = new Instance("PointLight");
		pointLight.Brightness = 2;
		pointLight.Color = Color3.fromRGB(100, 200, 255);
		pointLight.Range = this.config.arenaSize;
		pointLight.Parent = arena;

		// Create arena walls (invisible barriers)
		for (let i = 0; i < 4; i++) {
			const wall = new Instance("Part");
			wall.Name = `ArenaWall${i}`;
			wall.Size = new Vector3(2, 20, this.config.arenaSize * 2 + 4);
			wall.Transparency = 1;
			wall.CanCollide = true;
			wall.Anchored = true;
			wall.Parent = Workspace;

			// Position walls around arena
			switch (i) {
				case 0: // North
					wall.Position = this.config.arenaCenter.add(new Vector3(this.config.arenaSize + 2, 10, 0));
					break;
				case 1: // South
					wall.Position = this.config.arenaCenter.add(new Vector3(-this.config.arenaSize - 2, 10, 0));
					break;
				case 2: // East
					wall.Size = new Vector3(this.config.arenaSize * 2 + 4, 20, 2);
					wall.Position = this.config.arenaCenter.add(new Vector3(0, 10, this.config.arenaSize + 2));
					break;
				case 3: // West
					wall.Size = new Vector3(this.config.arenaSize * 2 + 4, 20, 2);
					wall.Position = this.config.arenaCenter.add(new Vector3(0, 10, -this.config.arenaSize - 2));
					break;
			}
		}

		print("⚔️ [ElementalBattle] Arena created successfully!");
	}

	private startNextWave(): void {
		this.gameState.currentWave++;
		this.gameState.enemiesInWave = math.floor(
			this.config.baseEnemiesPerWave * this.config.waveMultiplier ** (this.gameState.currentWave - 1),
		);
		this.gameState.enemiesRemaining = this.gameState.enemiesInWave;
		this.gameState.gamePhase = "playing";
		this.waveStartTime = tick();

		print(
			`⚔️ [ElementalBattle] Starting Wave ${this.gameState.currentWave} with ${this.gameState.enemiesInWave} enemies`,
		);

		// Spawn enemies for this wave
		this.spawnWaveEnemies();

		// Visual effect for wave start
		createParticleExplosion(this.config.arenaCenter, 50, Color3.fromRGB(255, 100, 100), [10, 30], [0.5, 1.5]);

		// Play wave start sound
		playSound("rbxasset://sounds/electronicpingshort.wav", 0.5);
	}

	private spawnWaveEnemies(): void {
		for (let i = 0; i < this.gameState.enemiesInWave; i++) {
			task.delay(i * 0.5, () => {
				// Stagger enemy spawning
				this.spawnEnemy();
			});
		}
	}

	private spawnEnemy(): void {
		// Random spawn position around arena edge
		const angle = math.random() * math.pi * 2;
		const spawnRadius = this.config.arenaSize * 0.8;
		const spawnPosition = this.config.arenaCenter.add(
			new Vector3(math.cos(angle) * spawnRadius, 2, math.sin(angle) * spawnRadius),
		);

		// Determine enemy type based on wave
		let enemyType: Enemy["enemyType"] = "basic";
		if (this.gameState.currentWave >= 3) {
			const typeRoll = math.random();
			if (typeRoll < 0.3) enemyType = "fast";
			else if (typeRoll < 0.6) enemyType = "heavy";
			else if (typeRoll < 0.8) enemyType = "elemental";
		}

		// Spawn entity
		const entity = this.entityManager.spawnEntity({
			type: EntityType.NPC,
			position: spawnPosition,
			data: {
				name: `Enemy_${enemyType}_${this.gameState.currentWave}`,
				aiEnabled: true,
				health: 50 * this.config.enemyHealthMultiplier ** (this.gameState.currentWave - 1),
			},
		});

		const entityId = entity.id;
		const enemy: Enemy = {
			entityId,
			health: 50 * this.config.enemyHealthMultiplier ** (this.gameState.currentWave - 1),
			maxHealth: 50 * this.config.enemyHealthMultiplier ** (this.gameState.currentWave - 1),
			enemyType,
			element:
				enemyType === "elemental"
					? (["fire", "ice", "earth"][math.random(0, 2)] as Enemy["element"])
					: undefined,
			position: spawnPosition,
			isAlive: true,
		};

		this.activeEnemies.set(entityId, enemy);

		// Configure AI for this enemy
		this.configureEnemyAI(entityId, enemyType);

		// Visual spawn effect
		createImpactFlash(spawnPosition, 4, 0.5);

		print(`⚔️ [ElementalBattle] Spawned ${enemyType} enemy with ${enemy.health} health`);
	}

	private configureEnemyAI(entityId: string, enemyType: Enemy["enemyType"]): void {
		const playerCharacter = this.player.Character;
		if (!playerCharacter) return;

		// Register AI agent with basic config
		const aiAgent = this.aiController.registerAI(entityId, {
			detectionRange: 50,
			moveSpeed: enemyType === "fast" ? 20 : enemyType === "heavy" ? 8 : 12,
			attackRange: 5,
		});

		print(`⚔️ [ElementalBattle] AI configured for ${enemyType} enemy: ${entityId}`);
	}

	private startGameLoop(): void {
		this.gameLoop = RunService.Heartbeat.Connect(() => {
			if (!this.gameState.isGameActive) return;

			// Update enemy states
			this.updateEnemies();

			// Check wave completion
			if (this.gameState.enemiesRemaining <= 0 && this.gameState.gamePhase === "playing") {
				this.onWaveCompleted();
			}

			// Update between wave timer
			if (this.gameState.gamePhase === "betweenWaves") {
				this.gameState.timeUntilNextWave = math.max(
					0,
					this.config.timeBetweenWaves - (tick() - this.waveStartTime),
				);
				if (this.gameState.timeUntilNextWave <= 0) {
					this.startNextWave();
				}
			}
		});
	}

	private updateEnemies(): void {
		this.activeEnemies.forEach((enemy, entityId) => {
			if (!enemy.isAlive) return;

			// Check if enemy entity still exists
			const entityData = this.entityManager.getEntity(entityId);
			if (!entityData) {
				// Enemy was destroyed, count as killed
				this.onEnemyKilled(entityId);
				return;
			}

			// Update enemy position if entity exists
			if (entityData && entityData.instance) {
				enemy.position = PositionHelper.getPosition(entityData.instance);
			}
		});
	}

	private onEnemyKilled(entityId: string): void {
		const enemy = this.activeEnemies.get(entityId);
		if (!enemy || !enemy.isAlive) return;

		enemy.isAlive = false;
		this.gameState.enemiesRemaining--;
		this.gameState.score += this.config.scorePerEnemyKill;
		this.gameState.experience += this.config.experiencePerEnemyKill;

		// Check for level up
		if (this.gameState.experience >= this.gameState.experienceToNextLevel) {
			this.levelUp();
		}

		// Visual effect for enemy death
		createParticleExplosion(enemy.position, 30, Color3.fromRGB(255, 255, 0), [5, 20], [0.3, 0.8]);

		// Play kill sound
		playSound("rbxasset://sounds/impact_generic_large_01.mp3", 0.3);

		// Remove from active enemies
		this.activeEnemies.delete(entityId);

		print(`⚔️ [ElementalBattle] Enemy killed! Score: ${this.gameState.score}, XP: ${this.gameState.experience}`);
	}

	private onWaveCompleted(): void {
		this.gameState.gamePhase = "betweenWaves";
		this.waveStartTime = tick();
		this.gameState.timeUntilNextWave = this.config.timeBetweenWaves;

		// Bonus score for completing wave
		const waveBonus = this.gameState.currentWave * 50;
		this.gameState.score += waveBonus;

		print(`⚔️ [ElementalBattle] Wave ${this.gameState.currentWave} completed! Bonus: ${waveBonus} points`);

		// Celebration effect
		createParticleExplosion(
			this.config.arenaCenter.add(new Vector3(0, 5, 0)),
			100,
			Color3.fromRGB(0, 255, 0),
			[10, 35],
			[0.5, 1.2],
		);
	}

	private levelUp(): void {
		this.gameState.playerLevel++;
		this.gameState.experience -= this.gameState.experienceToNextLevel;
		this.gameState.experienceToNextLevel = this.config.experiencePerLevel * this.gameState.playerLevel;

		// Increase player health
		this.gameState.maxPlayerHealth += 20;
		this.gameState.playerHealth = this.gameState.maxPlayerHealth; // Full heal on level up

		// Unlock new abilities
		this.unlockAbilities();

		print(`⚔️ [ElementalBattle] Level Up! Now level ${this.gameState.playerLevel}`);

		// Level up effect
		if (this.player.Character) {
			const characterPosition = PositionHelper.getPosition(this.player.Character);
			createParticleExplosion(characterPosition, 80, Color3.fromRGB(255, 215, 0), [10, 30], [0.4, 1.0]);
		}
	}

	private unlockAbilities(): void {
		const allAbilities = ["QUAKE_PUNCH", "FIRE_FIST", "ICE_AGE", "THREE_SWORD_STYLE", "ROOM"];

		if (this.gameState.playerLevel >= 3 && !this.gameState.abilitiesUnlocked.includes("ICE_AGE")) {
			this.gameState.abilitiesUnlocked.push("ICE_AGE");
			print("❄️ [ElementalBattle] Unlocked Ice Age ability!");
		}

		if (this.gameState.playerLevel >= 5 && !this.gameState.abilitiesUnlocked.includes("THREE_SWORD_STYLE")) {
			this.gameState.abilitiesUnlocked.push("THREE_SWORD_STYLE");
			print("⚔️ [ElementalBattle] Unlocked Three Sword Style ability!");
		}

		if (this.gameState.playerLevel >= 7 && !this.gameState.abilitiesUnlocked.includes("ROOM")) {
			this.gameState.abilitiesUnlocked.push("ROOM");
			print("🔵 [ElementalBattle] Unlocked Room ability!");
		}
	}

	public damagePlayer(damage: number): void {
		this.gameState.playerHealth = math.max(0, this.gameState.playerHealth - damage);

		if (this.gameState.playerHealth <= 0) {
			this.onPlayerDeath();
		}

		print(
			`⚔️ [ElementalBattle] Player took ${damage} damage. Health: ${this.gameState.playerHealth}/${this.gameState.maxPlayerHealth}`,
		);
	}

	private onPlayerDeath(): void {
		print("💀 [ElementalBattle] Player died! Game Over.");
		this.stop();
	}

	public async cleanup(): Promise<Result<void, Error>> {
		try {
			// Remove all active enemies
			this.activeEnemies.forEach((enemy, entityId) => {
				this.entityManager.destroyEntity(entityId);
				this.aiController.unregisterAI(entityId);
			});
			this.activeEnemies.clear();

			// Remove arena objects
			const arena = Workspace.FindFirstChild("ElementalArena");
			if (arena) arena.Destroy();

			for (let i = 0; i < 4; i++) {
				const wall = Workspace.FindFirstChild(`ArenaWall${i}`);
				if (wall) wall.Destroy();
			}

			print("⚔️ [ElementalBattle] Cleanup completed");
			return Result.ok(undefined);
		} catch (error) {
			const cleanupError = createError(`Failed to cleanup Elemental Battle: ${error}`, "CLEANUP_FAILED");
			warn(`❌ [ElementalBattle] ${cleanupError.message}`);
			return Result.err(cleanupError);
		}
	}
}
