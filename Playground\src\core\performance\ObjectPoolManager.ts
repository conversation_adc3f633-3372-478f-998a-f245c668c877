import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/BrandedTypes";

interface PoolableObject {
	reset(): void;
	isActive(): boolean;
	setActive(active: boolean): void;
}

interface PoolConfiguration {
	initialSize: number;
	maxSize: number;
	expandSize: number;
	shrinkThreshold: number;
	shrinkSize: number;
	enableAutoShrink: boolean;
	autoShrinkInterval: number;
}

interface PoolStats {
	totalCreated: number;
	totalDestroyed: number;
	currentActive: number;
	currentPooled: number;
	peakActive: number;
	poolHits: number;
	poolMisses: number;
	expansions: number;
	shrinks: number;
}

/**
 * Generic object pool for optimizing creation and destruction of frequently used objects
 */
class ObjectPool<T extends PoolableObject> {
	private pool: T[] = [];
	private activeObjects: Set<T> = new Set();
	private factory: () => T;
	private config: PoolConfiguration;
	private stats: PoolStats;
	private lastShrinkTime: number = 0;

	constructor(factory: () => T, config: Partial<PoolConfiguration> = {}) {
		this.factory = factory;
		this.config = {
			initialSize: 10,
			maxSize: 100,
			expandSize: 5,
			shrinkThreshold: 0.25, // Shrink when usage is below 25%
			shrinkSize: 5,
			enableAutoShrink: true,
			autoShrinkInterval: 30, // seconds
			...config,
		};

		this.stats = {
			totalCreated: 0,
			totalDestroyed: 0,
			currentActive: 0,
			currentPooled: 0,
			peakActive: 0,
			poolHits: 0,
			poolMisses: 0,
			expansions: 0,
			shrinks: 0,
		};

		this.initializePool();
	}

	/**
	 * Get an object from the pool
	 */
	public acquire(): T {
		let obj: T;

		if (this.pool.size() > 0) {
			obj = this.pool.pop()!;
			this.stats.poolHits++;
		} else {
			obj = this.createNewObject();
			this.stats.poolMisses++;

			// Consider expanding the pool if we're getting too many misses
			if (this.shouldExpand()) {
				this.expandPool();
			}
		}

		this.activeObjects.add(obj);
		obj.setActive(true);

		this.stats.currentActive = this.activeObjects.size();
		this.stats.currentPooled = this.pool.size();

		if (this.stats.currentActive > this.stats.peakActive) {
			this.stats.peakActive = this.stats.currentActive;
		}

		return obj;
	}

	/**
	 * Return an object to the pool
	 */
	public release(obj: T): void {
		if (!this.activeObjects.has(obj)) {
			warn("Attempted to release object that wasn't acquired from this pool");
			return;
		}

		this.activeObjects.delete(obj);
		obj.setActive(false);
		obj.reset();

		// Only add back to pool if we haven't exceeded max size
		if (this.pool.size() < this.config.maxSize) {
			this.pool.push(obj);
		} else {
			this.destroyObject(obj);
		}

		this.stats.currentActive = this.activeObjects.size();
		this.stats.currentPooled = this.pool.size();

		// Consider shrinking if enabled and it's time
		if (this.config.enableAutoShrink && this.shouldShrink()) {
			this.shrinkPool();
		}
	}

	/**
	 * Release all active objects back to the pool
	 */
	public releaseAll(): void {
		const activeArray = [...this.activeObjects];
		activeArray.forEach((obj) => this.release(obj));
	}

	/**
	 * Clear the entire pool and destroy all objects
	 */
	public clear(): void {
		// Release all active objects first
		this.releaseAll();

		// Destroy all pooled objects
		this.pool.forEach((obj) => this.destroyObject(obj));
		this.pool = [];

		this.stats.currentPooled = 0;
	}

	/**
	 * Get pool statistics
	 */
	public getStats(): PoolStats {
		return { ...this.stats };
	}

	/**
	 * Get pool configuration
	 */
	public getConfig(): PoolConfiguration {
		return { ...this.config };
	}

	/**
	 * Update pool configuration
	 */
	public updateConfig(newConfig: Partial<PoolConfiguration>): void {
		this.config = { ...this.config, ...newConfig };
	}

	/**
	 * Get efficiency metrics
	 */
	public getEfficiency(): {
		hitRate: number;
		utilizationRate: number;
		averagePoolSize: number;
	} {
		const totalRequests = this.stats.poolHits + this.stats.poolMisses;
		const hitRate = totalRequests > 0 ? this.stats.poolHits / totalRequests : 0;

		const totalCapacity = this.stats.currentActive + this.stats.currentPooled;
		const utilizationRate = totalCapacity > 0 ? this.stats.currentActive / totalCapacity : 0;

		return {
			hitRate,
			utilizationRate,
			averagePoolSize: (this.stats.currentActive + this.stats.currentPooled) / 2,
		};
	}

	private initializePool(): void {
		for (let i = 0; i < this.config.initialSize; i++) {
			const obj = this.createNewObject();
			this.pool.push(obj);
		}
		this.stats.currentPooled = this.pool.size();
	}

	private createNewObject(): T {
		const obj = this.factory();
		obj.setActive(false);
		this.stats.totalCreated++;
		return obj;
	}

	private destroyObject(obj: T): void {
		// In Roblox, we can't explicitly destroy objects, but we can mark them for GC
		obj.setActive(false);
		this.stats.totalDestroyed++;
	}

	private shouldExpand(): boolean {
		// Expand if we're consistently getting misses and haven't reached max size
		const totalRequests = this.stats.poolHits + this.stats.poolMisses;
		const recentMissRate = totalRequests > 10 ? this.stats.poolMisses / totalRequests : 0;

		return recentMissRate > 0.3 && this.pool.size() + this.config.expandSize <= this.config.maxSize;
	}

	private expandPool(): void {
		for (let i = 0; i < this.config.expandSize; i++) {
			if (this.pool.size() >= this.config.maxSize) break;

			const obj = this.createNewObject();
			this.pool.push(obj);
		}

		this.stats.expansions++;
		this.stats.currentPooled = this.pool.size();
		print(`🔧 Pool expanded to ${this.pool.size()} objects`);
	}

	private shouldShrink(): boolean {
		const currentTime = tick();

		// Only shrink at intervals
		if (currentTime - this.lastShrinkTime < this.config.autoShrinkInterval) {
			return false;
		}

		// Shrink if utilization is low
		const totalCapacity = this.stats.currentActive + this.stats.currentPooled;
		const utilization = totalCapacity > 0 ? this.stats.currentActive / totalCapacity : 0;

		return utilization < this.config.shrinkThreshold && this.pool.size() > this.config.shrinkSize;
	}

	private shrinkPool(): void {
		const shrinkAmount = math.min(this.config.shrinkSize, this.pool.size() - this.config.shrinkSize);

		for (let i = 0; i < shrinkAmount; i++) {
			const obj = this.pool.pop();
			if (obj) {
				this.destroyObject(obj);
			}
		}

		this.stats.shrinks++;
		this.stats.currentPooled = this.pool.size();
		this.lastShrinkTime = tick();
		print(`🔧 Pool shrunk to ${this.pool.size()} objects`);
	}
}

/**
 * Object Pool Manager service for managing multiple object pools
 */
export class ObjectPoolManager extends BaseService {
	private static instance: ObjectPoolManager;
	private pools: Map<string, ObjectPool<any>> = new Map();

	constructor() {
		super("ObjectPoolManager");
	}

	public static getInstance(): ObjectPoolManager {
		if (!ObjectPoolManager.instance) {
			ObjectPoolManager.instance = new ObjectPoolManager();
		}
		return ObjectPoolManager.instance;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		try {
			print("🏊 Object Pool Manager initialized");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to initialize ObjectPoolManager: ${error}`));
		}
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		// Clear all pools
		this.pools.forEach((pool) => pool.clear());
		this.pools.clear();

		print("🏊 Object Pool Manager shutdown");
		return Result.ok(undefined);
	}

	/**
	 * Register a new object pool
	 */
	public registerPool<T extends PoolableObject>(
		name: string,
		factory: () => T,
		config?: Partial<PoolConfiguration>,
	): ObjectPool<T> {
		if (this.pools.has(name)) {
			warn(`Pool '${name}' already exists. Replacing existing pool.`);
			this.pools.get(name)?.clear();
		}

		const pool = new ObjectPool(factory, config);
		this.pools.set(name, pool);

		print(`🏊 Registered object pool: ${name}`);
		return pool;
	}

	/**
	 * Get an existing object pool
	 */
	public getPool<T extends PoolableObject>(name: string): ObjectPool<T> | undefined {
		return this.pools.get(name) as ObjectPool<T>;
	}

	/**
	 * Remove and clear a pool
	 */
	public removePool(name: string): boolean {
		const pool = this.pools.get(name);
		if (pool) {
			pool.clear();
			this.pools.delete(name);
			print(`🏊 Removed object pool: ${name}`);
			return true;
		}
		return false;
	}

	/**
	 * Get all pool names
	 */
	public getPoolNames(): string[] {
		const names: string[] = [];
		this.pools.forEach((_, name) => names.push(name));
		return names;
	}

	/**
	 * Get statistics for all pools
	 */
	public getAllStats(): Record<string, PoolStats> {
		const stats: Record<string, PoolStats> = {};
		this.pools.forEach((pool, name) => {
			stats[name] = pool.getStats();
		});
		return stats;
	}

	/**
	 * Get efficiency metrics for all pools
	 */
	public getAllEfficiency(): Record<string, ReturnType<ObjectPool<any>["getEfficiency"]>> {
		const efficiency: Record<string, ReturnType<ObjectPool<any>["getEfficiency"]>> = {};
		this.pools.forEach((pool, name) => {
			efficiency[name] = pool.getEfficiency();
		});
		return efficiency;
	}

	/**
	 * Generate a comprehensive report of all pools
	 */
	public getReport(): string {
		let report = "🏊 Object Pool Manager Report\n";
		report += `Total Pools: ${this.pools.size()}\n\n`;

		this.pools.forEach((pool, name) => {
			const stats = pool.getStats();
			const efficiency = pool.getEfficiency();

			report += `📋 Pool: ${name}\n`;
			report += `  Active: ${stats.currentActive} | Pooled: ${stats.currentPooled} | Peak: ${stats.peakActive}\n`;
			report += `  Hit Rate: ${string.format("%.1f", efficiency.hitRate * 100)}% | Utilization: ${string.format("%.1f", efficiency.utilizationRate * 100)}%\n`;
			report += `  Total Created: ${stats.totalCreated} | Expansions: ${stats.expansions} | Shrinks: ${stats.shrinks}\n\n`;
		});

		return report;
	}
}

// Utility base class for creating poolable objects
export abstract class PoolableBase implements PoolableObject {
	private active = false;

	public isActive(): boolean {
		return this.active;
	}

	public setActive(active: boolean): void {
		this.active = active;
		if (active) {
			this.onActivate();
		} else {
			this.onDeactivate();
		}
	}

	public abstract reset(): void;

	protected onActivate(): void {
		// Override in subclasses if needed
	}

	protected onDeactivate(): void {
		// Override in subclasses if needed
	}
}

// Global object pooling functions for easy access
export const ObjectPooling = {
	getInstance: () => ObjectPoolManager.getInstance(),
	registerPool: <T extends PoolableObject>(name: string, factory: () => T, config?: Partial<PoolConfiguration>) =>
		ObjectPoolManager.getInstance().registerPool(name, factory, config),
	getPool: <T extends PoolableObject>(name: string) => ObjectPoolManager.getInstance().getPool<T>(name),
	getReport: () => ObjectPoolManager.getInstance().getReport(),
};

// Export types for external use
export type { PoolableObject, PoolConfiguration, PoolStats };
