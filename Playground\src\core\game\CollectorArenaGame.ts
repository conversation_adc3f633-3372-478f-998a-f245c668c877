import { GameController } from "./GameModeManager";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/RobloxError";
import { EntityManager } from "../entities/EntityManager";
import { EntityType } from "../entities/enums/EntityType";
import { AIController } from "../ai/AIController";
import { StateManager } from "../state/StateManager";
import { StateAction } from "../state/interfaces/StateAction";
import { RunService, Players, Workspace } from "@rbxts/services";
import { playSound } from "../effects/SoundHelper";
import { createParticleExplosion } from "../effects/ParticleHelper";
import { createImpactFlash } from "../effects/VisualEffectUtils";
import { PositionHelper } from "../helper/PositionHelper";
import { CollectorArenaScoreManager, GameScore } from "./CollectorArenaScoreManager";

export interface CollectorArenaState {
	score: number;
	coinsCollected: number;
	enemiesDefeated: number;
	timeRemaining: number;
	isGameActive: boolean;
	gamePhase: "waiting" | "playing" | "ended";
	difficulty: number;
}

export interface CollectorArenaConfig {
	gameDuration: number; // seconds
	arenaSize: number; // studs radius
	coinSpawnRate: number; // coins per second
	enemySpawnRate: number; // enemies per second
	maxCoins: number;
	maxEnemies: number;
	coinValue: number;
	enemyDefeatValue: number;
	arenaCenter: Vector3;
}

export class CollectorArenaGame implements GameController {
	private entityManager: EntityManager;
	private aiController: AIController;
	private scoreManager: CollectorArenaScoreManager;
	private gameLoop?: RBXScriptConnection;
	private coinSpawnLoop?: RBXScriptConnection;
	private enemySpawnLoop?: RBXScriptConnection;
	private config: CollectorArenaConfig;
	private spawnedCoins = new Set<string>();
	private spawnedEnemies = new Set<string>();
	private player: Player;
	private startTime: number = 0;

	// Simple state without StateManager for now
	private gameState: CollectorArenaState;

	constructor(config?: Partial<CollectorArenaConfig>) {
		this.player = Players.LocalPlayer;
		this.entityManager = EntityManager.getInstance();
		this.aiController = AIController.getInstance();
		this.scoreManager = CollectorArenaScoreManager.getInstance();

		// Default configuration
		this.config = {
			gameDuration: 120, // 2 minutes
			arenaSize: 50, // 50 stud radius
			coinSpawnRate: 2, // 2 coins per second
			enemySpawnRate: 0.5, // 1 enemy every 2 seconds
			maxCoins: 10,
			maxEnemies: 5,
			coinValue: 10,
			enemyDefeatValue: 25,
			arenaCenter: new Vector3(0, 5, 0),
			...config,
		};

		this.gameState = {
			score: 0,
			coinsCollected: 0,
			enemiesDefeated: 0,
			timeRemaining: this.config.gameDuration,
			isGameActive: false,
			gamePhase: "waiting",
			difficulty: 1,
		};

		print(`🏟️ [CollectorArena] Game initialized with config:`, this.config);
	}

	public getName(): string {
		return "Collector Arena";
	}

	public getGameState(): CollectorArenaState {
		return { ...this.gameState }; // Return a copy
	}

	public async start(): Promise<Result<void, Error>> {
		try {
			print("🏟️ [CollectorArena] Starting game...");

			// Reset game state
			this.startTime = tick();
			this.gameState = {
				score: 0,
				coinsCollected: 0,
				enemiesDefeated: 0,
				timeRemaining: this.config.gameDuration,
				isGameActive: true,
				gamePhase: "playing",
				difficulty: 1,
			};

			// Clear any existing entities
			await this.cleanup();

			// Create arena boundaries
			this.createArena();

			// Start game loops
			this.startGameLoop();
			this.startCoinSpawning();
			this.startEnemySpawning();

			// Move player to arena center
			const character = this.player.Character;
			if (character && character.FindFirstChild("HumanoidRootPart")) {
				const rootPart = character.FindFirstChild("HumanoidRootPart") as BasePart;
				rootPart.CFrame = new CFrame(this.config.arenaCenter);
			}

			// Play start sound and effect
			playSound("rbxasset://sounds/electronicpingshort.wav", 0.5);
			createParticleExplosion(this.config.arenaCenter, 20, Color3.fromRGB(0, 255, 0), [10, 30], [0.5, 1.5]);

			print("🏟️ [CollectorArena] Game started successfully!");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to start Collector Arena: ${error}`));
		}
	}

	public async stop(): Promise<Result<void, Error>> {
		try {
			print("🏟️ [CollectorArena] Stopping game...");

			this.gameState.isGameActive = false;
			this.gameState.gamePhase = "ended";

			// Stop all loops
			if (this.gameLoop) {
				this.gameLoop.Disconnect();
				this.gameLoop = undefined;
			}
			if (this.coinSpawnLoop) {
				this.coinSpawnLoop.Disconnect();
				this.coinSpawnLoop = undefined;
			}
			if (this.enemySpawnLoop) {
				this.enemySpawnLoop.Disconnect();
				this.enemySpawnLoop = undefined;
			}

			// Play end sound and effect
			playSound("rbxasset://sounds/button_rollover.wav", 0.5);
			createImpactFlash(this.config.arenaCenter, 30, 1);

			// Save the final score
			await this.saveFinalScore();

			print(`🏟️ [CollectorArena] Game ended! Final score: ${this.gameState.score}`);
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to stop Collector Arena: ${error}`));
		}
	}

	public async cleanup(): Promise<Result<void, Error>> {
		try {
			print("🏟️ [CollectorArena] Cleaning up game...");

			// Remove all spawned coins
			for (const coinId of this.spawnedCoins) {
				this.entityManager.destroyEntity(coinId);
			}
			this.spawnedCoins.clear();

			// Remove all spawned enemies
			for (const enemyId of this.spawnedEnemies) {
				this.entityManager.destroyEntity(enemyId);
			}
			this.spawnedEnemies.clear();

			// Remove arena boundaries
			const arena = Workspace.FindFirstChild("CollectorArena");
			if (arena) {
				arena.Destroy();
			}

			print("🏟️ [CollectorArena] Cleanup completed");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to cleanup Collector Arena: ${error}`));
		}
	}

	private createArena(): void {
		// Create arena container
		const arena = new Instance("Model");
		arena.Name = "CollectorArena";
		arena.Parent = Workspace;

		// Create arena floor
		const floor = new Instance("Part");
		floor.Name = "ArenaFloor";
		floor.Material = Enum.Material.Neon;
		floor.BrickColor = new BrickColor("Bright green");
		floor.Anchored = true;
		floor.CanCollide = true;
		floor.Size = new Vector3(this.config.arenaSize * 2, 1, this.config.arenaSize * 2);
		floor.Position = this.config.arenaCenter.sub(new Vector3(0, 2, 0));
		floor.Parent = arena;

		// Create arena walls (invisible barriers)
		const wallHeight = 10;
		const wallThickness = 1;

		// North wall
		const northWall = this.createWall(
			new Vector3(this.config.arenaSize * 2, wallHeight, wallThickness),
			this.config.arenaCenter.add(new Vector3(0, wallHeight / 2, this.config.arenaSize)),
		);
		northWall.Parent = arena;

		// South wall
		const southWall = this.createWall(
			new Vector3(this.config.arenaSize * 2, wallHeight, wallThickness),
			this.config.arenaCenter.add(new Vector3(0, wallHeight / 2, -this.config.arenaSize)),
		);
		southWall.Parent = arena;

		// East wall
		const eastWall = this.createWall(
			new Vector3(wallThickness, wallHeight, this.config.arenaSize * 2),
			this.config.arenaCenter.add(new Vector3(this.config.arenaSize, wallHeight / 2, 0)),
		);
		eastWall.Parent = arena;

		// West wall
		const westWall = this.createWall(
			new Vector3(wallThickness, wallHeight, this.config.arenaSize * 2),
			this.config.arenaCenter.add(new Vector3(-this.config.arenaSize, wallHeight / 2, 0)),
		);
		westWall.Parent = arena;

		print("🏟️ [CollectorArena] Arena created");
	}

	private createWall(size: Vector3, position: Vector3): Part {
		const wall = new Instance("Part");
		wall.Name = "ArenaWall";
		wall.Material = Enum.Material.ForceField;
		wall.BrickColor = new BrickColor("Bright blue");
		wall.Anchored = true;
		wall.CanCollide = true;
		wall.Transparency = 0.8;
		wall.Size = size;
		wall.Position = position;
		return wall;
	}

	private startGameLoop(): void {
		this.gameLoop = RunService.Heartbeat.Connect(() => {
			if (!this.gameState.isGameActive) return;

			// Update timer (using a fixed delta time)
			const deltaTime = 1 / 60; // Assume 60 FPS
			const newTimeRemaining = this.gameState.timeRemaining - deltaTime;

			if (newTimeRemaining <= 0) {
				// Game over
				this.stop();
				return;
			}

			this.gameState.timeRemaining = newTimeRemaining;
			this.gameState.difficulty = math.floor(1 + (this.config.gameDuration - newTimeRemaining) / 30); // Increase difficulty every 30 seconds

			// Check for coin collection
			this.checkCoinCollection();
		});
	}

	private startCoinSpawning(): void {
		let lastCoinSpawn = tick();

		this.coinSpawnLoop = RunService.Heartbeat.Connect(() => {
			if (!this.gameState.isGameActive) return;
			if (this.spawnedCoins.size() >= this.config.maxCoins) return;

			const now = tick();
			const timeSinceLastSpawn = now - lastCoinSpawn;
			const spawnInterval = 1 / this.config.coinSpawnRate;

			if (timeSinceLastSpawn >= spawnInterval) {
				this.spawnCoin();
				lastCoinSpawn = now;
			}
		});
	}

	private startEnemySpawning(): void {
		let lastEnemySpawn = tick();

		this.enemySpawnLoop = RunService.Heartbeat.Connect(() => {
			if (!this.gameState.isGameActive) return;
			if (this.spawnedEnemies.size() >= this.config.maxEnemies) return;

			const now = tick();
			const timeSinceLastSpawn = now - lastEnemySpawn;
			const spawnInterval = 1 / (this.config.enemySpawnRate * this.gameState.difficulty);

			if (timeSinceLastSpawn >= spawnInterval) {
				this.spawnEnemy();
				lastEnemySpawn = now;
			}
		});
	}

	private spawnCoin(): void {
		const spawnPosition = this.getRandomArenaPosition();

		const coinEntity = this.entityManager.spawnEntity({
			type: EntityType.Pickup,
			position: spawnPosition,
			data: {
				name: "Coin",
				value: this.config.coinValue,
				collectionDistance: 5,
			},
		});

		// Make coin visible
		if (coinEntity.instance) {
			const part = coinEntity.instance as Part;
			part.Material = Enum.Material.Neon;
			part.BrickColor = new BrickColor("Bright yellow");
			part.Shape = Enum.PartType.Ball;
			part.Size = new Vector3(2, 2, 2);
			part.CanCollide = false;
			part.Anchored = true;

			// Add spinning animation
			const spinConnection = RunService.Heartbeat.Connect(() => {
				if (part.Parent) {
					part.CFrame = part.CFrame.mul(CFrame.Angles(0, 0.1, 0));
				} else {
					spinConnection.Disconnect();
				}
			});
		}

		this.spawnedCoins.add(coinEntity.id);

		// Play spawn sound
		playSound("rbxasset://sounds/pickup_01.wav", 0.3);

		print(`🪙 [CollectorArena] Spawned coin at ${spawnPosition}`);
	}

	private spawnEnemy(): void {
		const spawnPosition = this.getRandomArenaPosition();

		const enemyEntity = this.entityManager.spawnEntity({
			type: EntityType.NPC,
			position: spawnPosition,
			data: {
				name: "Enemy",
				health: 100,
				speed: 10,
				detectionRange: 20,
				isHostile: true,
			},
		});

		// Make enemy visible and add AI
		if (enemyEntity.instance) {
			const npcModel = enemyEntity.instance as Model;
			const torso = npcModel.FindFirstChild("Torso") as Part;
			if (torso) {
				torso.Material = Enum.Material.Neon;
				torso.BrickColor = new BrickColor("Bright red");
				torso.Size = new Vector3(4, 4, 4);
				torso.CanCollide = true;
				torso.Anchored = false;
			}

			// Add AI behavior
			this.aiController.registerAI(enemyEntity.id, {
				detectionRange: 20,
				followRange: 25,
				attackRange: 5,
				moveSpeed: 10 + this.gameState.difficulty * 2,
				patrolRadius: 10,
				reactionTime: 0.5,
				aggroTime: 5,
				memoryDuration: 3,
			});
		}

		this.spawnedEnemies.add(enemyEntity.id);

		// Play spawn sound
		playSound("rbxasset://sounds/impact_03.wav", 0.4);

		print(`👹 [CollectorArena] Spawned enemy at ${spawnPosition}`);
	}

	private getRandomArenaPosition(): Vector3 {
		const angle = math.random() * math.pi * 2;
		const distance = math.random() * (this.config.arenaSize - 5); // Keep away from walls

		const x = this.config.arenaCenter.X + math.cos(angle) * distance;
		const z = this.config.arenaCenter.Z + math.sin(angle) * distance;
		const y = this.config.arenaCenter.Y;

		return new Vector3(x, y, z);
	}

	private checkCoinCollection(): void {
		const character = this.player.Character;
		if (!character || !character.FindFirstChild("HumanoidRootPart")) return;

		const playerPosition = (character.FindFirstChild("HumanoidRootPart") as BasePart).Position;

		for (const coinId of this.spawnedCoins) {
			const coinEntity = this.entityManager.getEntity(coinId);
			if (!coinEntity || !coinEntity.instance) continue;

			const coinPosition = (coinEntity.instance as BasePart).Position;
			const distance = playerPosition.sub(coinPosition).Magnitude;

			if (distance <= ((coinEntity.data?.collectionDistance as number) || 5)) {
				this.collectCoin(coinId, (coinEntity.data?.value as number) || this.config.coinValue);
			}
		}
	}

	private collectCoin(coinId: string, value: number): void {
		// Remove coin
		this.entityManager.destroyEntity(coinId);
		this.spawnedCoins.delete(coinId);

		// Update score
		this.gameState.score += value;
		this.gameState.coinsCollected += 1;

		// Play collection effects
		const character = this.player.Character;
		if (character && character.FindFirstChild("HumanoidRootPart")) {
			const playerPosition = (character.FindFirstChild("HumanoidRootPart") as BasePart).Position;
			playSound("rbxasset://sounds/pickup_01.wav", 0.6);
			createParticleExplosion(playerPosition, 10, Color3.fromRGB(255, 255, 0), [5, 15], [0.2, 0.8]);
		}

		print(`🪙 [CollectorArena] Coin collected! Score: ${this.gameState.score}`);
	}

	private async saveFinalScore(): Promise<void> {
		try {
			const gameScore: GameScore = {
				playerName: this.player.Name,
				score: this.gameState.score,
				coinsCollected: this.gameState.coinsCollected,
				enemiesDefeated: this.gameState.enemiesDefeated,
				gameTime: tick() - this.startTime,
				timestamp: tick(),
			};

			const saved = await this.scoreManager.saveScore(gameScore);
			if (saved) {
				print(`💾 [CollectorArena] Score saved successfully!`);
			} else {
				warn(`⚠️ [CollectorArena] Failed to save score`);
			}
		} catch (error) {
			warn(`❌ [CollectorArena] Error saving score: ${error}`);
		}
	}

	public async getPersonalBest(): Promise<GameScore | undefined> {
		return await this.scoreManager.getPersonalBest();
	}

	public async getRecentGames(): Promise<GameScore[]> {
		return await this.scoreManager.getRecentGames();
	}
}
