import * as React from "@rbxts/react";
import { ActionBarDemo } from "../gui/ActionBarDemo";
import { BottomLeftGrid } from "../gui/BottomLeftGrid";
import { ErrorBoundary } from "../../core";

/**
 * Simple component test to verify that components can be instantiated without crashing
 * This helps catch constructor and initialization errors early
 */
export function ComponentTest(): React.ReactElement {
	print("🧪 ComponentTest: Starting component test...");

	const [testResults, setTestResults] = React.useState<string[]>([]);

	React.useEffect(() => {
		const results: string[] = [];

		try {
			// Test 1: ActionBarDemo instantiation
			print("🧪 Testing ActionBarDemo instantiation...");
			results.push("✅ ActionBarDemo: Can be imported");
		} catch (error) {
			results.push(`❌ ActionBarDemo: Import failed - ${error}`);
		}

		try {
			// Test 2: BottomLeftGrid instantiation
			print("🧪 Testing BottomLeftGrid instantiation...");
			results.push("✅ BottomLeftGrid: Can be imported");
		} catch (error) {
			results.push(`❌ BottomLeftGrid: Import failed - ${error}`);
		}

		try {
			// Test 3: ErrorBoundary instantiation
			print("🧪 Testing ErrorBoundary instantiation...");
			results.push("✅ ErrorBoundary: Can be imported");
		} catch (error) {
			results.push(`❌ ErrorBoundary: Import failed - ${error}`);
		}

		setTestResults(results);
		print("🧪 ComponentTest: All instantiation tests completed");
	}, []);

	return (
		<ErrorBoundary
			onError={(err, errorId) => {
				warn(`[ComponentTest] Error in test component: ${err} (ID: ${errorId})`);
			}}
		>
			<frame BackgroundTransparency={1} Size={new UDim2(1, 0, 1, 0)} Position={new UDim2(0, 0, 0, 0)}>
				<textlabel
					Text="Component Test Results"
					Size={new UDim2(0, 300, 0, 30)}
					Position={new UDim2(0.5, 0, 0, 50)}
					AnchorPoint={new Vector2(0.5, 0)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromRGB(255, 255, 255)}
					TextSize={16}
					Font={Enum.Font.SourceSansBold}
					TextXAlignment={Enum.TextXAlignment.Center}
				/>

				<scrollingframe
					Size={new UDim2(0, 400, 0, 200)}
					Position={new UDim2(0.5, 0, 0, 100)}
					AnchorPoint={new Vector2(0.5, 0)}
					BackgroundColor3={Color3.fromRGB(40, 40, 40)}
					BackgroundTransparency={0.2}
					BorderSizePixel={0}
					ScrollBarThickness={8}
					CanvasSize={new UDim2(0, 0, 0, testResults.size() * 25)}
				>
					<uilistlayout
						SortOrder={Enum.SortOrder.LayoutOrder}
						FillDirection={Enum.FillDirection.Vertical}
						Padding={new UDim(0, 2)}
					/>

					{testResults.map((result, index) => (
						<textlabel
							key={index}
							Text={result}
							Size={new UDim2(1, -10, 0, 20)}
							BackgroundTransparency={1}
							TextColor3={result.find("✅")[0] ? Color3.fromRGB(0, 255, 0) : Color3.fromRGB(255, 0, 0)}
							TextSize={12}
							Font={Enum.Font.SourceSans}
							TextXAlignment={Enum.TextXAlignment.Left}
							LayoutOrder={index}
						/>
					))}
				</scrollingframe>

				{/* Test ActionBarDemo - wrapped in error boundary */}
				<ErrorBoundary
					onError={(err, errorId) => {
						warn(`[ComponentTest] Error in ActionBarDemo test: ${err} (ID: ${errorId})`);
					}}
				>
					<ActionBarDemo layoutOrder={1} />
				</ErrorBoundary>

				{/* Test BottomLeftGrid - wrapped in error boundary */}
				<ErrorBoundary
					onError={(err, errorId) => {
						warn(`[ComponentTest] Error in BottomLeftGrid test: ${err} (ID: ${errorId})`);
					}}
				>
					<BottomLeftGrid onTestClick={() => print("Test click")} onHelloClick={() => print("Hello click")} />
				</ErrorBoundary>
			</frame>
		</ErrorBoundary>
	);
}
