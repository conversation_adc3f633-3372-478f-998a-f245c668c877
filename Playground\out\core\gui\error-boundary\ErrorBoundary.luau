-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
-- Simple functional error boundary that catches errors in children
local function ErrorBoundary(props)
	local hasError, setHasError = React.useState(false)
	local errorData, setErrorData = React.useState(nil)
	local errorId, setErrorId = React.useState("")
	local errorIdCounterRef = React.useRef(0)
	-- Reset error state when children change
	React.useEffect(function()
		if hasError then
			setHasError(false)
			setErrorData(nil)
			setErrorId("")
		end
	end, { props.children })
	local handleRetry = React.useCallback(function()
		setHasError(false)
		setErrorData(nil)
		setErrorId("")
	end, {})
	-- Error boundary logic using try-catch in useEffect
	local renderChildren = React.useMemo(function()
		if hasError then
			return nil
		end
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { props.children }
		end, function(err)
			-- Catch synchronous errors
			local errorMessage = if type(err) == "string" then err else tostring(err)
			-- Generate error ID safely
			errorIdCounterRef.current = errorIdCounterRef.current + 1
			local newErrorId = `error_boundary_{errorIdCounterRef.current}_{tick()}`
			-- Simple logging without dependencies
			warn(`[ErrorBoundary] React component error: {errorMessage}`)
			warn(`[ErrorBoundary] Error ID: {newErrorId}`)
			-- Set error state
			setHasError(true)
			setErrorData(err)
			setErrorId(newErrorId)
			-- Call custom error handler if provided
			if props.onError then
				TS.try(function()
					props.onError(err, newErrorId)
				end, function(handlerError)
					warn(`[ErrorBoundary] Error handler failed: {handlerError}`)
				end)
			end
			return TS.TRY_RETURN, { nil }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end, { props.children, hasError, props.onError })
	if hasError then
		-- Use custom fallback if provided
		if props.fallback and errorId ~= "" then
			local _exitType, _returns = TS.try(function()
				return TS.TRY_RETURN, { props.fallback(errorData, errorId, handleRetry) }
			end, function(fallbackError)
				warn(`[ErrorBoundary] Custom fallback failed: {fallbackError}`)
				-- Fall through to default UI
			end)
			if _exitType then
				return unpack(_returns)
			end
		end
		-- Safe default error UI that won't cause more errors
		return React.createElement("frame", {
			Size = UDim2.new(1, 0, 1, 0),
			Position = UDim2.new(0, 0, 0, 0),
			BackgroundColor3 = Color3.fromHex("#2a2a2a"),
			BackgroundTransparency = 0,
			BorderSizePixel = 2,
			BorderColor3 = Color3.fromHex("#ff4444"),
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, 8),
		}), React.createElement("uipadding", {
			PaddingTop = UDim.new(0, 16),
			PaddingBottom = UDim.new(0, 16),
			PaddingLeft = UDim.new(0, 16),
			PaddingRight = UDim.new(0, 16),
		}), React.createElement("uilistlayout", {
			SortOrder = Enum.SortOrder.LayoutOrder,
			FillDirection = Enum.FillDirection.Vertical,
			HorizontalAlignment = Enum.HorizontalAlignment.Center,
			VerticalAlignment = Enum.VerticalAlignment.Center,
			Padding = UDim.new(0, 8),
		}), React.createElement("textlabel", {
			Text = "🚨 Component Error",
			Size = UDim2.new(1, 0, 0, 24),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex("#ff4444"),
			TextSize = 18,
			Font = Enum.Font.SourceSansBold,
			TextXAlignment = Enum.TextXAlignment.Center,
			LayoutOrder = 1,
		}), React.createElement("textlabel", {
			Text = "This component encountered an error and has been safely contained.",
			Size = UDim2.new(1, 0, 0, 40),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex("#cccccc"),
			TextSize = 14,
			Font = Enum.Font.SourceSans,
			TextXAlignment = Enum.TextXAlignment.Center,
			TextWrapped = true,
			LayoutOrder = 2,
		}), if errorId ~= nil and errorId ~= "" then (React.createElement("textlabel", {
			Text = `Error ID: {errorId}`,
			Size = UDim2.new(1, 0, 0, 16),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex("#888888"),
			TextSize = 12,
			Font = Enum.Font.SourceSans,
			TextXAlignment = Enum.TextXAlignment.Center,
			LayoutOrder = 3,
		})) else (React.createElement("frame", {
			Size = UDim2.new(0, 0, 0, 0),
			BackgroundTransparency = 1,
		})), React.createElement("textbutton", {
			Text = "Try Again",
			Size = UDim2.new(0, 100, 0, 32),
			BackgroundColor3 = Color3.fromHex("#4a9eff"),
			BorderSizePixel = 0,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 14,
			Font = Enum.Font.SourceSansBold,
			LayoutOrder = 4,
			Event = {
				Activated = handleRetry,
			},
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, 4),
		})))
	end
	-- Render children if no error
	if renderChildren ~= nil then
		return renderChildren
	end
	-- Return empty frame if no children
	return React.createElement("frame", {
		Size = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
	})
end
return {
	ErrorBoundary = ErrorBoundary,
}
