import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY } from "../../design";

interface LoadingSpinnerProps {
	size?: number;
	color?: string;
	speed?: number;
}

export const LoadingSpinner = React.memo((props: LoadingSpinnerProps) => {
	const size = props.size ?? 20;
	const color = props.color ?? COLORS.primary;
	const speed = props.speed ?? 1;

	return (
		<textlabel
			Size={new UDim2(0, size, 0, size)}
			BackgroundTransparency={1}
			Text="●"
			TextColor3={Color3.fromHex(color)}
			TextSize={size}
			Font={Enum.Font.Gotham}
			TextXAlignment={Enum.TextXAlignment.Center}
			TextYAlignment={Enum.TextYAlignment.Center}
		/>
	);
});

interface LoadingIndicatorProps {
	text?: string;
	size?: "sm" | "md" | "lg";
}

export function LoadingIndicator(props: LoadingIndicatorProps) {
	const currentSize = {
		spinner: props.size === "lg" ? 24 : props.size === "sm" ? 16 : 20,
		text: props.size === "lg" ? 18 : props.size === "sm" ? 12 : 14,
	};

	return (
		<frame
			Size={new UDim2(0, 0, 0, currentSize.spinner + (props.text ? currentSize.text + 10 : 0))}
			BackgroundTransparency={1}
			AutomaticSize={Enum.AutomaticSize.X}
		>
			<uilistlayout
				FillDirection={Enum.FillDirection.Vertical}
				HorizontalAlignment={Enum.HorizontalAlignment.Center}
				VerticalAlignment={Enum.VerticalAlignment.Center}
				Padding={new UDim(0, 4)}
			/>

			<LoadingSpinner size={currentSize.spinner} speed={1} />

			{props.text ? (
				<textlabel
					Size={new UDim2(0, 0, 0, currentSize.text)}
					BackgroundTransparency={1}
					Text={props.text}
					TextSize={currentSize.text}
					Font={Enum.Font.Gotham}
					TextColor3={Color3.fromHex(COLORS.text.main)}
					AutomaticSize={Enum.AutomaticSize.X}
				/>
			) : undefined}
		</frame>
	);
}
