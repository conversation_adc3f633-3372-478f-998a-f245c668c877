-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local function Frame(props)
	local _condition = props.backgroundColor
	if _condition == nil then
		_condition = COLORS.bg.base
	end
	local backgroundColor = _condition
	local _condition_1 = props.backgroundTransparency
	if _condition_1 == nil then
		_condition_1 = 0
	end
	local backgroundTransparency = _condition_1
	local _condition_2 = props.padding
	if _condition_2 == nil then
		_condition_2 = 0
	end
	local padding = _condition_2
	return React.createElement("frame", {
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		BackgroundTransparency = backgroundTransparency,
		Size = props.size or UDim2.new(1, 0, 1, 0),
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		BorderSizePixel = 0,
		ZIndex = props.zIndex,
		AutomaticSize = props.autoSize or Enum.AutomaticSize.None,
		ClipsDescendants = true,
	}, if padding > 0 then (React.createElement("uipadding", {
		PaddingTop = UDim.new(0, padding),
		PaddingBottom = UDim.new(0, padding),
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	})) else nil, props.children)
end
return {
	Frame = Frame,
}
