import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { ContainerFrame } from "../frame";
import { ResponsiveManager } from "../layout/ResponsiveManager";
import { QuickUtils } from "../../helper/GameUtils";

interface GridProps {
	rows: number;
	cols: number;
	children?: Array<React.Element>;
	layoutOrder?: number;
	cellType?: "button" | "label" | "card";
	responsive?: boolean;
	maxWidth?: number;
	spacing?: number; // Custom spacing between cells
	elevation?: boolean; // Add subtle shadows to cells
}

export function Grid(props: GridProps) {
	const cellType = props.cellType ?? "card";
	const responsiveManager = ResponsiveManager.getInstance();
	const spacing = props.spacing ?? SIZES.margin.sm;

	// Calculate modern, responsive cell sizes
	let cellWidth: number;
	let cellHeight: number;

	if (props.responsive) {
		const screenSize = responsiveManager.getScreenSize();
		const deviceType = responsiveManager.getDeviceType();

		// Enhanced base sizes for modern design
		const baseCellWidth = cellType === "button" ? 100 : cellType === "card" ? 120 : SIZES.gridCell.width;
		const baseCellHeight = cellType === "button" ? 60 : cellType === "card" ? 80 : SIZES.gridCell.height;

		// Improved responsive scaling
		const scaleFactor = deviceType === "mobile" ? 0.85 : deviceType === "tablet" ? 0.92 : 1.0;

		cellWidth = baseCellWidth * scaleFactor;
		cellHeight = baseCellHeight * scaleFactor;

		// Responsive max width handling
		if (props.maxWidth) {
			const availableWidth = props.maxWidth - spacing * (props.cols - 1);
			const maxCellWidth = availableWidth / props.cols;
			cellWidth = math.min(cellWidth, maxCellWidth);
		}
	} else {
		cellWidth = cellType === "button" ? 100 : cellType === "card" ? 120 : SIZES.gridCell.width;
		cellHeight = cellType === "button" ? 60 : cellType === "card" ? 80 : SIZES.gridCell.height;
	}

	const gridWidth = cellWidth * props.cols + spacing * (props.cols - 1);
	const gridHeight = cellHeight * props.rows + spacing * (props.rows - 1);

	// Get modern styling for cell type
	const getCellStyling = () => {
		switch (cellType) {
			case "button":
				return {
					backgroundColor: "transparent",
					backgroundTransparency: 1,
					cornerRadius: 0,
					borderColor: "transparent",
					borderThickness: 0,
					padding: 0,
				};
			case "card":
				return {
					backgroundColor: COLORS.bg.base,
					backgroundTransparency: 0,
					cornerRadius: BORDER_RADIUS.card,
					borderColor: COLORS.border.base,
					borderThickness: 1,
					padding: SIZES.padding.sm,
				};
			default: // label
				return {
					backgroundColor: COLORS.bg.secondary,
					backgroundTransparency: 0,
					cornerRadius: BORDER_RADIUS.sm,
					borderColor: COLORS.border.l2,
					borderThickness: 1,
					padding: SIZES.padding.xs,
				};
		}
	};

	const cellStyling = getCellStyling();

	return (
		<frame
			Size={new UDim2(0, gridWidth, 0, gridHeight)}
			Position={new UDim2(0, 0, 0, 0)}
			BackgroundTransparency={1}
			BorderSizePixel={0}
			LayoutOrder={props.layoutOrder}
		>
			<uigridlayout
				CellPadding={new UDim2(0, spacing, 0, spacing)}
				CellSize={new UDim2(0, cellWidth, 0, cellHeight)}
				SortOrder={Enum.SortOrder.LayoutOrder}
				HorizontalAlignment={Enum.HorizontalAlignment.Center}
				VerticalAlignment={Enum.VerticalAlignment.Center}
			/>

			{props.children?.map((child, index) => (
				<frame
					key={`grid-cell-${index}`}
					Size={new UDim2(1, 0, 1, 0)}
					BackgroundColor3={QuickUtils.safeColor(cellStyling.backgroundColor, COLORS.bg.base)}
					BackgroundTransparency={cellStyling.backgroundTransparency}
					BorderSizePixel={0}
					LayoutOrder={index}
				>
					{/* Modern rounded corners */}
					{cellStyling.cornerRadius > 0 && <uicorner CornerRadius={new UDim(0, cellStyling.cornerRadius)} />}

					{/* Modern borders */}
					{cellStyling.borderThickness > 0 ? (
						<uistroke
							Color={QuickUtils.safeColor(cellStyling.borderColor, COLORS.border.base)}
							Thickness={cellStyling.borderThickness}
							Transparency={0.3}
						/>
					) : undefined}

					{/* Subtle elevation shadow for cards */}
					{props.elevation && cellType === "card" ? (
						<frame
							Size={new UDim2(1, 2, 1, 2)}
							Position={new UDim2(0, 1, 0, 1)}
							BackgroundColor3={Color3.fromRGB(0, 0, 0)}
							BackgroundTransparency={0.95}
							BorderSizePixel={0}
							ZIndex={-1}
						>
							<uicorner CornerRadius={new UDim(0, cellStyling.cornerRadius)} />
						</frame>
					) : undefined}

					{/* Cell padding */}
					{cellStyling.padding > 0 ? (
						<uipadding
							PaddingLeft={new UDim(0, cellStyling.padding)}
							PaddingRight={new UDim(0, cellStyling.padding)}
							PaddingTop={new UDim(0, cellStyling.padding)}
							PaddingBottom={new UDim(0, cellStyling.padding)}
						/>
					) : undefined}

					{child}
				</frame>
			))}
		</frame>
	);
}
