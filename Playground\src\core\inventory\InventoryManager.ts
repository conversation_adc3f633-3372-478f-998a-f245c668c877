import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/BrandedTypes";
import {
	ItemDefinition,
	InventorySlot,
	InventoryConfiguration,
	InventoryStats,
	ItemTransaction,
	ItemType,
	ItemRarity,
} from "./types";

/**
 * Individual inventory instance for a player or container
 */
export class Inventory {
	private slots: InventorySlot[] = [];
	private config: InventoryConfiguration;
	private transactions: ItemTransaction[] = [];
	private maxTransactionHistory = 100;
	private changeCallbacks: ((inventory: Inventory) => void)[] = [];

	constructor(config: InventoryConfiguration) {
		this.config = { ...config };
		this.initializeSlots();
	}

	/**
	 * Add an item to the inventory
	 */
	public addItem(item: ItemDefinition, quantity: number = 1): Result<boolean, string> {
		if (quantity <= 0) {
			return Result.err("Quantity must be positive");
		}

		// Check weight limit
		if (!this.canFitWeight(item.weight * quantity)) {
			return Result.err("Not enough weight capacity");
		}

		// Try to stack with existing items first
		if (this.config.autoStack) {
			const stackResult = this.tryStackItem(item, quantity);
			if (stackResult.isOk() && stackResult.getValue() === 0) {
				this.recordTransaction("add", item.id, quantity);
				this.notifyChange();
				return Result.ok(true);
			}
			quantity = stackResult.getValue();
		}

		// Find empty slots for remaining quantity
		const emptySlots = this.getEmptySlots();
		const slotsNeeded = math.ceil(quantity / item.maxStack);

		if (emptySlots.size() < slotsNeeded && !this.config.allowOverflow) {
			return Result.err("Not enough inventory space");
		}

		// Add items to empty slots
		let remainingQuantity = quantity;
		for (const slot of emptySlots) {
			if (remainingQuantity <= 0) break;

			const slotQuantity = math.min(remainingQuantity, item.maxStack);
			this.slots[slot.slotIndex] = {
				item: { ...item },
				quantity: slotQuantity,
				slotIndex: slot.slotIndex,
				locked: false,
			};

			remainingQuantity -= slotQuantity;
		}

		this.recordTransaction("add", item.id, quantity - remainingQuantity);
		this.notifyChange();

		return remainingQuantity > 0
			? Result.err(`Could only add ${quantity - remainingQuantity} items due to space constraints`)
			: Result.ok(true);
	}

	/**
	 * Remove an item from the inventory
	 */
	public removeItem(itemId: string, quantity: number = 1): Result<boolean, string> {
		if (quantity <= 0) {
			return Result.err("Quantity must be positive");
		}

		const itemSlots = this.findItemSlots(itemId);
		if (itemSlots.size() === 0) {
			return Result.err("Item not found in inventory");
		}

		// Calculate total available quantity
		const totalAvailable = itemSlots.reduce((sum, slot) => sum + slot.quantity, 0);
		if (totalAvailable < quantity) {
			return Result.err(`Not enough items (available: ${totalAvailable}, requested: ${quantity})`);
		}

		// Remove items from slots
		let remainingToRemove = quantity;
		for (const slot of itemSlots) {
			if (remainingToRemove <= 0) break;

			const removeFromSlot = math.min(remainingToRemove, slot.quantity);
			slot.quantity -= removeFromSlot;
			remainingToRemove -= removeFromSlot;

			// Clear slot if empty
			if (slot.quantity === 0) {
				this.slots[slot.slotIndex] = {
					item: undefined,
					quantity: 0,
					slotIndex: slot.slotIndex,
					locked: false,
				};
			}
		}

		this.recordTransaction("remove", itemId, quantity);
		this.notifyChange();
		return Result.ok(true);
	}

	/**
	 * Move an item from one slot to another
	 */
	public moveItem(fromSlot: number, toSlot: number, quantity?: number): Result<boolean, string> {
		if (fromSlot === toSlot) {
			return Result.err("Cannot move item to the same slot");
		}

		if (!this.isValidSlotIndex(fromSlot) || !this.isValidSlotIndex(toSlot)) {
			return Result.err("Invalid slot index");
		}

		const sourceSlot = this.slots[fromSlot];
		const targetSlot = this.slots[toSlot];

		if (!sourceSlot.item) {
			return Result.err("Source slot is empty");
		}

		if (sourceSlot.locked) {
			return Result.err("Source slot is locked");
		}

		const moveQuantity = quantity || sourceSlot.quantity;
		if (moveQuantity > sourceSlot.quantity) {
			return Result.err("Not enough items in source slot");
		}

		// If target slot is empty, simple move
		if (!targetSlot.item) {
			targetSlot.item = { ...sourceSlot.item };
			targetSlot.quantity = moveQuantity;

			sourceSlot.quantity -= moveQuantity;
			if (sourceSlot.quantity === 0) {
				sourceSlot.item = undefined;
			}

			this.recordTransaction("move", sourceSlot.item!.id, moveQuantity, fromSlot, toSlot);
			this.notifyChange();
			return Result.ok(true);
		}

		// If target slot has the same item, try to stack
		if (targetSlot.item.id === sourceSlot.item.id) {
			const availableSpace = targetSlot.item.maxStack - targetSlot.quantity;
			const actualMoveQuantity = math.min(moveQuantity, availableSpace);

			if (actualMoveQuantity > 0) {
				targetSlot.quantity += actualMoveQuantity;
				sourceSlot.quantity -= actualMoveQuantity;

				if (sourceSlot.quantity === 0) {
					sourceSlot.item = undefined;
				}

				this.recordTransaction("move", sourceSlot.item!.id, actualMoveQuantity, fromSlot, toSlot);
				this.notifyChange();
				return Result.ok(true);
			}
		}

		return Result.err("Cannot move items to occupied slot with different item");
	}

	/**
	 * Split a stack of items
	 */
	public splitStack(slotIndex: number, splitQuantity: number): Result<number, string> {
		if (!this.isValidSlotIndex(slotIndex)) {
			return Result.err("Invalid slot index");
		}

		const slot = this.slots[slotIndex];
		if (!slot.item || slot.quantity <= 1) {
			return Result.err("Cannot split single item or empty slot");
		}

		if (splitQuantity >= slot.quantity) {
			return Result.err("Split quantity must be less than current quantity");
		}

		// Find empty slot for split items
		const emptySlot = this.getEmptySlots()[0];
		if (!emptySlot) {
			return Result.err("No empty slot available for split");
		}

		// Create new stack in empty slot
		emptySlot.item = { ...slot.item };
		emptySlot.quantity = splitQuantity;
		slot.quantity -= splitQuantity;

		this.recordTransaction("split", slot.item.id, splitQuantity, slotIndex, emptySlot.slotIndex);
		this.notifyChange();
		return Result.ok(emptySlot.slotIndex);
	}

	/**
	 * Get item count for a specific item
	 */
	public getItemCount(itemId: string): number {
		return this.slots.filter((slot) => slot.item?.id === itemId).reduce((sum, slot) => sum + slot.quantity, 0);
	}

	/**
	 * Check if inventory has enough of an item
	 */
	public hasItem(itemId: string, quantity: number = 1): boolean {
		return this.getItemCount(itemId) >= quantity;
	}

	/**
	 * Get all items of a specific type
	 */
	public getItemsByType(itemType: ItemType): InventorySlot[] {
		return this.slots.filter((slot) => slot.item?.type === itemType);
	}

	/**
	 * Get all items of a specific rarity
	 */
	public getItemsByRarity(rarity: ItemRarity): InventorySlot[] {
		return this.slots.filter((slot) => slot.item?.rarity === rarity);
	}

	/**
	 * Search for items by name or description
	 */
	public searchItems(query: string): InventorySlot[] {
		if (!this.config.enableSearch) {
			return [];
		}

		const lowerQuery = query.lower();
		return this.slots.filter((slot) => {
			if (!slot.item) return false;
			return (
				slot.item.name.lower().find(lowerQuery)[0] !== undefined ||
				slot.item.description.lower().find(lowerQuery)[0] !== undefined
			);
		});
	}

	/**
	 * Sort inventory automatically
	 */
	public sortInventory(): void {
		if (!this.config.autoSort) return;

		const itemSlots = this.slots.filter((slot) => slot.item !== undefined);

		// Custom sort function that returns -1, 0, or 1
		function sortInventorySlots(itemSlots: InventorySlot[]): InventorySlot[] {
			const sorted = [...itemSlots];
			for (let i = 0; i < sorted.size() - 1; i++) {
				for (let j = i + 1; j < sorted.size(); j++) {
					const a = sorted[i];
					const b = sorted[j];

					if (!a.item || !b.item) continue;

					let shouldSwap = false;
					if (a.item.type !== b.item.type) {
						shouldSwap = a.item.type > b.item.type;
					} else if (a.item.rarity !== b.item.rarity) {
						shouldSwap = a.item.rarity > b.item.rarity;
					} else {
						shouldSwap = a.item.name > b.item.name;
					}

					if (shouldSwap) {
						const temp = sorted[i];
						sorted[i] = sorted[j];
						sorted[j] = temp;
					}
				}
			}
			return sorted;
		}

		const sortedSlots = sortInventorySlots(itemSlots);

		// Clear all slots
		this.slots.forEach((slot) => {
			slot.item = undefined;
			slot.quantity = 0;
		});

		// Refill with sorted items
		sortedSlots.forEach((slot, index) => {
			this.slots[index].item = slot.item;
			this.slots[index].quantity = slot.quantity;
		});

		this.notifyChange();
	}

	/**
	 * Get inventory statistics
	 */
	public getStats(): InventoryStats {
		const stats: InventoryStats = {
			totalItems: 0,
			totalWeight: 0,
			totalValue: 0,
			itemsByType: {} as Record<ItemType, number>,
			itemsByRarity: {} as Record<ItemRarity, number>,
			emptySlots: 0,
			occupancyRate: 0,
		};

		// Initialize counters
		stats.itemsByType[ItemType.Weapon] = 0;
		stats.itemsByType[ItemType.Armor] = 0;
		stats.itemsByType[ItemType.Consumable] = 0;
		stats.itemsByType[ItemType.Material] = 0;
		stats.itemsByType[ItemType.Quest] = 0;
		stats.itemsByType[ItemType.Currency] = 0;
		stats.itemsByType[ItemType.Cosmetic] = 0;
		stats.itemsByType[ItemType.Tool] = 0;
		stats.itemsByType[ItemType.Key] = 0;
		stats.itemsByType[ItemType.Collectible] = 0;

		stats.itemsByRarity[ItemRarity.Common] = 0;
		stats.itemsByRarity[ItemRarity.Uncommon] = 0;
		stats.itemsByRarity[ItemRarity.Rare] = 0;
		stats.itemsByRarity[ItemRarity.Epic] = 0;
		stats.itemsByRarity[ItemRarity.Legendary] = 0;
		stats.itemsByRarity[ItemRarity.Mythic] = 0;

		// Calculate stats
		this.slots.forEach((slot) => {
			if (slot.item) {
				stats.totalItems += slot.quantity;
				stats.totalWeight += slot.item.weight * slot.quantity;
				stats.totalValue += slot.item.value * slot.quantity;
				stats.itemsByType[slot.item.type] += slot.quantity;
				stats.itemsByRarity[slot.item.rarity] += slot.quantity;
			} else {
				stats.emptySlots++;
			}
		});

		stats.occupancyRate = (this.config.size - stats.emptySlots) / this.config.size;

		return stats;
	}

	/**
	 * Get all slots
	 */
	public getSlots(): InventorySlot[] {
		return [...this.slots];
	}

	/**
	 * Get a specific slot
	 */
	public getSlot(index: number): InventorySlot | undefined {
		return this.isValidSlotIndex(index) ? this.slots[index] : undefined;
	}

	/**
	 * Lock or unlock a slot
	 */
	public setSlotLocked(index: number, locked: boolean): boolean {
		if (!this.isValidSlotIndex(index)) return false;

		this.slots[index].locked = locked;
		this.notifyChange();
		return true;
	}

	/**
	 * Add a change callback
	 */
	public onChange(callback: (inventory: Inventory) => void): () => void {
		this.changeCallbacks.push(callback);

		// Return unsubscribe function
		return () => {
			const index = this.changeCallbacks.indexOf(callback);
			if (index >= 0) {
				this.changeCallbacks.remove(index);
			}
		};
	}

	/**
	 * Get transaction history
	 */
	public getTransactionHistory(): ItemTransaction[] {
		return [...this.transactions];
	}

	/**
	 * Clear transaction history
	 */
	public clearTransactionHistory(): void {
		this.transactions = [];
	}

	/**
	 * Export inventory data
	 */
	public exportData(): {
		slots: InventorySlot[];
		config: InventoryConfiguration;
		stats: InventoryStats;
	} {
		return {
			slots: this.getSlots(),
			config: { ...this.config },
			stats: this.getStats(),
		};
	}

	private initializeSlots(): void {
		this.slots = [];
		for (let i = 0; i < this.config.size; i++) {
			this.slots.push({
				item: undefined,
				quantity: 0,
				slotIndex: i,
				locked: false,
			});
		}
	}

	private tryStackItem(item: ItemDefinition, quantity: number): Result<number, string> {
		const existingSlots = this.findItemSlots(item.id);
		let remainingQuantity = quantity;

		for (const slot of existingSlots) {
			if (remainingQuantity <= 0) break;
			if (!slot.item) continue;

			const availableSpace = slot.item.maxStack - slot.quantity;
			const addToSlot = math.min(remainingQuantity, availableSpace);

			if (addToSlot > 0) {
				slot.quantity += addToSlot;
				remainingQuantity -= addToSlot;
			}
		}

		return Result.ok(remainingQuantity);
	}

	private findItemSlots(itemId: string): InventorySlot[] {
		return this.slots.filter((slot) => slot.item?.id === itemId);
	}

	private getEmptySlots(): InventorySlot[] {
		return this.slots.filter((slot) => !slot.item && !slot.locked);
	}

	private canFitWeight(additionalWeight: number): boolean {
		const currentWeight = this.getStats().totalWeight;
		return currentWeight + additionalWeight <= this.config.maxWeight;
	}

	private isValidSlotIndex(index: number): boolean {
		return index >= 0 && index < this.slots.size();
	}

	private recordTransaction(
		transactionType: ItemTransaction["type"],
		itemId: string,
		quantity: number,
		fromSlot?: number,
		toSlot?: number,
	): void {
		const transaction: ItemTransaction = {
			id: `txn_${tick()}_${math.random()}`,
			type: transactionType,
			itemId,
			quantity,
			fromSlot,
			toSlot,
			timestamp: tick(),
		};

		this.transactions.push(transaction);

		// Limit transaction history
		if (this.transactions.size() > this.maxTransactionHistory) {
			this.transactions.remove(0);
		}
	}

	private notifyChange(): void {
		this.changeCallbacks.forEach((callback) => {
			try {
				callback(this);
			} catch (error) {
				warn(`Inventory change callback failed: ${error}`);
			}
		});
	}
}

/**
 * Inventory Manager service for managing multiple inventories
 */
export class InventoryManager extends BaseService {
	private static instance: InventoryManager;
	private inventories: Map<string, Inventory> = new Map();
	private itemDefinitions: Map<string, ItemDefinition> = new Map();

	constructor() {
		super("InventoryManager");
	}

	public static getInstance(): InventoryManager {
		if (!InventoryManager.instance) {
			InventoryManager.instance = new InventoryManager();
		}
		return InventoryManager.instance;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		try {
			this.loadDefaultItems();
			print("🎒 Inventory Manager initialized");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to initialize InventoryManager: ${error}`));
		}
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.inventories.clear();
		this.itemDefinitions.clear();
		print("🎒 Inventory Manager shutdown");
		return Result.ok(undefined);
	}

	/**
	 * Create a new inventory
	 */
	public createInventory(id: string, config: InventoryConfiguration): Result<Inventory, string> {
		if (this.inventories.has(id)) {
			return Result.err(`Inventory '${id}' already exists`);
		}

		const inventory = new Inventory(config);
		this.inventories.set(id, inventory);

		print(`🎒 Created inventory: ${id}`);
		return Result.ok(inventory);
	}

	/**
	 * Get an existing inventory
	 */
	public getInventory(id: string): Inventory | undefined {
		return this.inventories.get(id);
	}

	/**
	 * Remove an inventory
	 */
	public removeInventory(id: string): boolean {
		const removed = this.inventories.delete(id);
		if (removed) {
			print(`🎒 Removed inventory: ${id}`);
		}
		return removed;
	}

	/**
	 * Register an item definition
	 */
	public registerItem(item: ItemDefinition): Result<void, string> {
		if (this.itemDefinitions.has(item.id)) {
			return Result.err(`Item '${item.id}' already registered`);
		}

		this.itemDefinitions.set(item.id, { ...item });
		return Result.ok(undefined);
	}

	/**
	 * Get an item definition
	 */
	public getItemDefinition(itemId: string): ItemDefinition | undefined {
		return this.itemDefinitions.get(itemId);
	}

	/**
	 * Get all item definitions
	 */
	public getAllItemDefinitions(): ItemDefinition[] {
		const items: ItemDefinition[] = [];
		this.itemDefinitions.forEach((item) => items.push(item));
		return items;
	}

	/**
	 * Create an item instance from definition
	 */
	public createItem(itemId: string, overrides?: Partial<ItemDefinition>): ItemDefinition | undefined {
		const definition = this.itemDefinitions.get(itemId);
		if (!definition) return undefined;

		return { ...definition, ...overrides };
	}

	/**
	 * Get inventory statistics for all inventories
	 */
	public getAllInventoryStats(): Record<string, InventoryStats> {
		const stats: Record<string, InventoryStats> = {};
		this.inventories.forEach((inventory, id) => {
			stats[id] = inventory.getStats();
		});
		return stats;
	}

	/**
	 * Generate a comprehensive report
	 */
	public getReport(): string {
		let report = "🎒 Inventory Manager Report\n";
		report += `Total Inventories: ${this.inventories.size()}\n`;
		report += `Registered Items: ${this.itemDefinitions.size()}\n\n`;

		this.inventories.forEach((inventory, id) => {
			const stats = inventory.getStats();
			report += `📋 Inventory: ${id}\n`;
			report += `  Items: ${stats.totalItems} | Empty Slots: ${stats.emptySlots}\n`;
			report += `  Weight: ${string.format("%.1f", stats.totalWeight)} | Value: ${stats.totalValue}\n`;
			report += `  Occupancy: ${string.format("%.1f", stats.occupancyRate * 100)}%\n\n`;
		});

		return report;
	}

	private loadDefaultItems(): void {
		// Load some default item definitions for testing
		const defaultItems: Partial<ItemDefinition>[] = [
			{
				id: "health_potion",
				name: "Health Potion",
				description: "Restores health over time",
				type: ItemType.Consumable,
				rarity: ItemRarity.Common,
				maxStack: 10,
				value: 50,
				weight: 0.5,
			},
			{
				id: "iron_sword",
				name: "Iron Sword",
				description: "A sturdy iron sword",
				type: ItemType.Weapon,
				rarity: ItemRarity.Common,
				maxStack: 1,
				value: 200,
				weight: 3.0,
			},
		];

		defaultItems.forEach((item) => {
			if (item.id) {
				this.registerItem(item as ItemDefinition);
			}
		});
	}
}

// Global inventory management functions for easy access
export const InventorySystem = {
	getInstance: () => InventoryManager.getInstance(),
	createInventory: (id: string, config: InventoryConfiguration) =>
		InventoryManager.getInstance().createInventory(id, config),
	getInventory: (id: string) => InventoryManager.getInstance().getInventory(id),
	getReport: () => InventoryManager.getInstance().getReport(),
};
