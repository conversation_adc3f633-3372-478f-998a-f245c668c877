-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local FormField = React.memo(function(props)
	local focused, setFocused = React.useState(false)
	local hasInteracted, setHasInteracted = React.useState(false)
	-- Memoize validation state
	local validationState = React.useMemo(function()
		local hasError = props.error ~= nil and props.error ~= ""
		local isRequired = props.required == true
		local isEmpty = props.value == ""
		local showError = hasError and hasInteracted
		local showRequiredIndicator = isRequired and not hasError
		return {
			hasError = hasError,
			isEmpty = isEmpty,
			showError = showError,
			showRequiredIndicator = showRequiredIndicator,
		}
	end, { props.error, props.required, props.value, hasInteracted })
	-- Memoize colors based on state
	local colors = React.useMemo(function()
		local _binding = validationState
		local hasError = _binding.hasError
		local showError = _binding.showError
		return {
			border = if showError then COLORS.border.danger elseif focused then COLORS.border.focus else COLORS.border.l2,
			background = if props.disabled then COLORS.bg.secondary elseif focused then COLORS.bg.surface else COLORS.bg.base,
			text = if props.disabled then COLORS.text.secondary else COLORS.text.main,
			label = if hasError then COLORS.error else COLORS.text.secondary,
		}
	end, { validationState, focused, props.disabled })
	-- Handle blur with validation
	local handleBlur = React.useCallback(function()
		setFocused(false)
		setHasInteracted(true)
		local _result = props.onBlur
		if _result ~= nil then
			_result()
		end
	end, { props.onBlur })
	-- Handle focus
	local handleFocus = React.useCallback(function()
		setFocused(true)
	end, {})
	-- Handle text change with validation
	local handleTextChanged = React.useCallback(function(textBox)
		local newValue = textBox.Text
		-- Apply max length constraint
		if props.maxLength ~= nil and #newValue > props.maxLength then
			local _newValue = newValue
			local _maxLength = props.maxLength
			newValue = string.sub(_newValue, 1, _maxLength)
			textBox.Text = newValue
		end
		-- Type-specific validation
		if props.type == "number" then
			-- Allow only numbers and decimal point
			local numbersOnly = (string.gsub(newValue, "[^%d%.]", ""))
			if numbersOnly ~= newValue then
				newValue = numbersOnly
				textBox.Text = newValue
			end
		end
		props.onChange(newValue)
	end, { props.onChange, props.maxLength, props.type })
	local fieldHeight = 36
	local _value = props.label
	local labelHeight = if _value ~= "" and _value then 20 else 0
	local errorHeight = if validationState.showError then 18 else 0
	local totalHeight = labelHeight + fieldHeight + errorHeight + (if labelHeight > 0 then 4 else 0) + (if errorHeight > 0 then 4 else 0)
	local _value_1 = props.label
	local _exp = if _value_1 ~= "" and _value_1 then (React.createElement("textlabel", {
		Text = props.label .. (if validationState.showRequiredIndicator then " *" else ""),
		Size = UDim2.new(1, 0, 0, labelHeight),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(colors.label),
		TextSize = 13,
		Font = Enum.Font.Gotham,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
	})) else nil
	local _attributes = {
		Text = props.value,
	}
	local _condition = props.placeholder
	if _condition == nil then
		_condition = ""
	end
	_attributes.PlaceholderText = _condition
	_attributes.Size = UDim2.new(1, 0, 0, fieldHeight)
	local _value_2 = props.label
	_attributes.Position = UDim2.new(0, 0, 0, labelHeight + (if _value_2 ~= "" and _value_2 then 4 else 0))
	_attributes.BackgroundColor3 = Color3.fromHex(colors.background)
	_attributes.BorderSizePixel = 0
	_attributes.TextColor3 = Color3.fromHex(colors.text)
	_attributes.PlaceholderColor3 = Color3.fromHex(COLORS.text.muted)
	_attributes.TextSize = SIZES.fontSize.md
	_attributes.Font = Enum.Font.Gotham
	_attributes.TextXAlignment = Enum.TextXAlignment.Left
	_attributes.ClearTextOnFocus = false
	_attributes.TextEditable = not props.disabled
	_attributes.Event = {
		Focused = handleFocus,
		FocusLost = handleBlur,
	}
	_attributes.Change = {
		Text = handleTextChanged,
	}
	local _exp_1 = React.createElement("textbox", _attributes, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(colors.border),
		Thickness = if focused then 2 else 1,
		Transparency = 0,
	}), React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, 12),
		PaddingRight = UDim.new(0, 12),
		PaddingTop = UDim.new(0, 8),
		PaddingBottom = UDim.new(0, 8),
	}))
	local _result
	if validationState.showError then
		local _attributes_1 = {
			Text = props.error,
			Size = UDim2.new(1, 0, 0, errorHeight),
		}
		local _exp_2 = labelHeight + fieldHeight
		local _value_3 = props.label
		_attributes_1.Position = UDim2.new(0, 0, 0, _exp_2 + (if _value_3 ~= "" and _value_3 then 8 else 4))
		_attributes_1.BackgroundTransparency = 1
		_attributes_1.TextColor3 = Color3.fromHex(COLORS.error)
		_attributes_1.TextSize = 12
		_attributes_1.Font = Enum.Font.Gotham
		_attributes_1.TextXAlignment = Enum.TextXAlignment.Left
		_attributes_1.TextYAlignment = Enum.TextYAlignment.Center
		_result = (React.createElement("textlabel", _attributes_1))
	else
		_result = nil
	end
	return React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, totalHeight),
		BackgroundTransparency = 1,
		LayoutOrder = props.layoutOrder,
	}, _exp, _exp_1, _result)
end)
-- Validation helpers
local validators = {
	required = function(value)
		return if #value == 0 then "This field is required" else nil
	end,
	minLength = function(min)
		return function(value)
			return if #value < min then `Must be at least {min} characters` else nil
		end
	end,
	maxLength = function(max)
		return function(value)
			return if #value > max then `Must be no more than {max} characters` else nil
		end
	end,
	email = function(value)
		local emailPattern = "^[%w%._%+-]+@[%w%.%-]+%.[%a]+$"
		local _value = (string.match(value, emailPattern))
		return if _value ~= 0 and _value == _value and _value ~= "" and _value then nil else "Please enter a valid email address"
	end,
	number = function(value)
		local num = tonumber(value)
		return if num ~= nil then nil else "Please enter a valid number"
	end,
	range = function(min, max)
		return function(value)
			local num = tonumber(value)
			if num == nil then
				return "Please enter a valid number"
			end
			if num < min or num > max then
				return `Value must be between {min} and {max}`
			end
			return nil
		end
	end,
}
-- Simplified form validation for roblox-ts
local function useFormValidation(initialValues, fieldNames, validationRules)
	local values, setValues = React.useState(initialValues)
	local errors, setErrors = React.useState({})
	local touchedFields, setTouchedFields = React.useState({})
	-- Validate a single field
	local validateField = React.useCallback(function(field, value)
		local rules = validationRules[field] or {}
		for i = 0, #rules - 1 do
			local rule = rules[i + 1]
			local validationError = rule(value)
			if validationError ~= "" and validationError then
				return validationError
			end
		end
		return nil
	end, { validationRules })
	-- Set field value with validation
	local setValue = React.useCallback(function(field, value)
		setValues(function(prev)
			local _object = table.clone(prev)
			setmetatable(_object, nil)
			_object[field] = value
			return _object
		end)
		-- Validate immediately if field has been touched
		if touchedFields[field] then
			local validationError = validateField(field, value)
			setErrors(function(prev)
				local _object = table.clone(prev)
				setmetatable(_object, nil)
				_object[field] = validationError
				return _object
			end)
		end
	end, { touchedFields, validateField })
	-- Mark field as touched and validate
	local setFieldTouched = React.useCallback(function(field)
		setTouchedFields(function(prev)
			local _object = table.clone(prev)
			setmetatable(_object, nil)
			_object[field] = true
			return _object
		end)
		local validationError = validateField(field, values[field])
		setErrors(function(prev)
			local _object = table.clone(prev)
			setmetatable(_object, nil)
			_object[field] = validationError
			return _object
		end)
	end, { validateField, values })
	-- Validate all fields
	local validateAll = React.useCallback(function()
		local newErrors = {}
		local hasErrors = false
		-- Validate each field by name
		for i = 0, #fieldNames - 1 do
			local field = fieldNames[i + 1]
			local validationError = validateField(field, values[field])
			if validationError ~= "" and validationError then
				newErrors[field] = validationError
				hasErrors = true
			end
		end
		setErrors(newErrors)
		return not hasErrors
	end, { fieldNames, validateField, values })
	-- Check if form is valid
	local isValid = React.useMemo(function()
		-- Check each field for errors
		for i = 0, #fieldNames - 1 do
			local field = fieldNames[i + 1]
			if errors[field] ~= nil and errors[field] ~= "" then
				return false
			end
		end
		return true
	end, { errors, fieldNames })
	return {
		values = values,
		errors = errors,
		touched = touchedFields,
		isValid = isValid,
		setValue = setValue,
		setTouched = setFieldTouched,
		validateAll = validateAll,
	}
end
return {
	useFormValidation = useFormValidation,
	FormField = FormField,
	validators = validators,
}
