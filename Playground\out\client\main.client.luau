-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local createRoot = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react-roblox").createRoot
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local ReplicatedStorage = _services.ReplicatedStorage
local ElementalBattleUI = TS.import(script, script.Parent, "gui", "ElementalBattleUI").ElementalBattleUI
local MovementExample = TS.import(script, script.Parent, "movement", "MovementExample").MovementExample
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local initializeDebugSystem = _core.initializeDebugSystem
local initializeClientCore = _core.initializeClientCore
local SplashScreen = _core.SplashScreen
local useSplashScreen = _core.useSplashScreen
local ToastManager = _core.ToastManager
local ToastService = _core.ToastService
local ErrorBoundary = _core.ErrorBoundary
local COLORS = _core.COLORS
local SIZES = _core.SIZES
local BORDER_RADIUS = _core.BORDER_RADIUS
local version = "v1.3.5"
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
-- Create a ScreenGui with properties to make it visible
local screenGui = Instance.new("ScreenGui", playerGui)
screenGui.ResetOnSpawn = false
screenGui.IgnoreGuiInset = true
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
screenGui.DisplayOrder = 100
screenGui.Name = "MainReactGUI"
print(`🎮 [{tick()}] Main React ScreenGui created with DisplayOrder: {screenGui.DisplayOrder}`)
-- Main App Component with Splash Screen - Focus on Elemental Battle Game
local function MainApp()
	local _binding = useSplashScreen()
	local state = _binding.state
	local startLoading = _binding.startLoading
	local hide = _binding.hide
	local setupDefaultTasks = _binding.setupDefaultTasks
	local manager = _binding.manager
	local hasStarted, setHasStarted = React.useState(false)
	local gameUIOpen, setGameUIOpen = React.useState(false)
	-- Setup and start loading when component mounts
	React.useEffect(function()
		if not hasStarted then
			print("🚀 MainApp: Starting initialization process...")
			-- Setup default core framework loading tasks
			setupDefaultTasks()
			-- Add custom game loading tasks with better error handling
			manager:addLoadingTask({
				name = "Waiting for Server...",
				weight = 1,
				task = TS.async(function()
					TS.try(function()
						print("🔍 Waiting for server to be ready...")
						-- Wait for server initialization indicator
						local serverIndicator = ReplicatedStorage:WaitForChild("CoreServerInitialized", 30)
						if serverIndicator and serverIndicator.Value then
							print("✅ Server is ready!")
						else
							warn("⚠️ Server not ready, but continuing...")
						end
					end, function(error)
						warn(`⚠️ Failed to detect server readiness: {error}`)
						-- Continue anyway
					end)
				end),
			})
			manager:addLoadingTask({
				name = "Loading Game Systems...",
				weight = 2,
				task = TS.async(function()
					TS.try(function()
						-- Initialize debug system for development
						print("🔧 Initializing debug system...")
						initializeDebugSystem()
						-- Initialize client core
						print("🏗️ Initializing client core...")
						local clientCoreResult = TS.await(initializeClientCore())
						if clientCoreResult:isError() then
							local errorMessage = `Failed to initialize client core: {clientCoreResult:getError().message}`
							warn(errorMessage)
							error(errorMessage)
						end
						print("✅ Game systems loaded successfully")
					end, function(error)
						warn(`⚠️ Game systems loading failed: {error}`)
						-- Don't throw - allow the app to continue with limited functionality
					end)
				end),
			})
			manager:addLoadingTask({
				name = "Initializing Movement System...",
				weight = 1,
				task = function()
					TS.try(function()
						-- Initialize movement example for testing
						print("🏃 Initializing movement system...")
						MovementExample.new()
						print("✅ Movement system initialized")
					end, function(error)
						warn(`⚠️ Movement system initialization failed: {error}`)
						-- Continue without movement - not critical for UI
					end)
				end,
			})
			manager:addLoadingTask({
				name = "Finalizing Elemental Battle Setup...",
				weight = 1,
				task = TS.async(function()
					TS.try(function()
						print(`⚔️ Elemental Battle Arena loaded! [{version}]`)
						print(`🎮 Press Enter or click the floating button to open the game menu`)
						-- Validate critical systems are working
						local players = game:GetService("Players")
						local localPlayer = players.LocalPlayer
						if not localPlayer then
							warn("⚠️ LocalPlayer not available - some features may not work")
						end
						-- Show success toast when loading is complete (with delay to ensure toast system is ready)
						task.delay(0.5, function()
							TS.try(function()
								ToastService.showSuccess("⚔️ Elemental Battle Arena", `Ready for epic battles! v{version}`)
							end, function(toastError)
								warn(`⚠️ Failed to show welcome toast: {toastError}`)
							end)
						end)
						print("🎯 Elemental Battle client setup finalized successfully")
					end, function(error)
						warn(`⚠️ Client setup finalization failed: {error}`)
						-- Continue anyway - basic functionality should still work
					end)
				end),
			})
			-- Start the loading process
			print("🎬 Starting loading process...")
			startLoading():catch(function(loadingError)
				warn(`❌ Loading process failed: {loadingError}`)
				-- Force hide splash screen if loading completely fails
				task.delay(2, function()
					hide()
					ToastService.showError("Loading Error", "Some systems failed to load, but the application is still functional.")
				end)
			end)
			setHasStarted(true)
		end
	end, { hasStarted, setupDefaultTasks, manager, startLoading })
	local handleLoadingComplete = React.useCallback(function()
		-- Called when loading is complete and splash screen should hide
		hide()
		print("🎉 Splash screen loading completed!")
		-- Auto-open the game UI after loading
		task.delay(1, function()
			setGameUIOpen(true)
		end)
	end, { hide })
	-- Handle keyboard input for opening game UI
	React.useEffect(function()
		local UserInputService = game:GetService("UserInputService")
		local connection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then
				return nil
			end
			if input.UserInputType == Enum.UserInputType.Keyboard then
				if input.KeyCode == Enum.KeyCode.Return then
					-- Enter key
					setGameUIOpen(true)
				end
			end
		end)
		return function()
			return connection:Disconnect()
		end
	end, {})
	return React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[MainApp] Critical error in main application: {err} (ID: {errorId})`)
			-- Don't crash the entire app - show a toast notification instead
			TS.try(function()
				ToastService.showError("Application Error", `A component error occurred. Error ID: {errorId}`)
			end, function(toastError)
				warn(`[MainApp] Failed to show error toast: {toastError}`)
			end)
		end,
	}, React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
	}, React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[SplashScreen] Error in splash screen: {err} (ID: {errorId})`)
		end,
	}, React.createElement(SplashScreen, {
		isVisible = state.isVisible,
		loadingProgress = state.loadingProgress,
		loadingText = state.loadingText,
		onLoadingComplete = handleLoadingComplete,
	})), React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ToastManager] Error in toast system: {err} (ID: {errorId})`)
		end,
	}, React.createElement(ToastManager)), if not state.isVisible then (React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
	}, React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[FloatingButton] Error in floating button: {err} (ID: {errorId})`)
		end,
	}, React.createElement("frame", {
		Size = UDim2.new(0, 95, 0, 95),
		Position = UDim2.new(0, 20, 1, -115),
		AnchorPoint = Vector2.new(0, 1),
		BackgroundColor3 = Color3.fromHex(COLORS.primary),
		BackgroundTransparency = 0.05,
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 47),
	}), " ", React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.accent),
		Thickness = 2,
		Transparency = 0.3,
	}), React.createElement("frame", {
		Size = UDim2.new(1, 8, 1, 8),
		Position = UDim2.new(0, 4, 0, 4),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromRGB(0, 0, 0),
		BackgroundTransparency = 0.75,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 51),
	})), React.createElement("frame", {
		Size = UDim2.new(0.8, 0, 0.4, 0),
		Position = UDim2.new(0.1, 0, 0.1, 0),
		BackgroundColor3 = Color3.fromRGB(255, 255, 255),
		BackgroundTransparency = 0.9,
		BorderSizePixel = 0,
		ZIndex = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 20),
	})), React.createElement("textbutton", {
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		Text = "⚔️",
		TextSize = 38,
		TextColor3 = Color3.fromHex(COLORS.text.inverse),
		Font = Enum.Font.FredokaOne,
		Event = {
			MouseButton1Click = function()
				return setGameUIOpen(true)
			end,
			MouseEnter = function(rbx)
				-- Enhanced hover effect
				local frame = rbx.Parent
				if frame then
					frame.BackgroundColor3 = Color3.fromHex(COLORS["primary-hover"])
					frame.BackgroundTransparency = 0
				end
			end,
			MouseLeave = function(rbx)
				-- Reset color with transparency
				local frame = rbx.Parent
				if frame then
					frame.BackgroundColor3 = Color3.fromHex(COLORS.primary)
					frame.BackgroundTransparency = 0.05
				end
			end,
		},
	}), React.createElement("textlabel", {
		Size = UDim2.new(0, 240, 0, 28),
		Position = UDim2.new(1, 20, 0.5, -14),
		AnchorPoint = Vector2.new(0, 0.5),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.base),
		BackgroundTransparency = 0.15,
		BorderSizePixel = 0,
		Text = "⚔️ Elemental Battle Arena",
		TextSize = SIZES.fontSize.lg,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		Font = Enum.Font.GothamBold,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextStrokeTransparency = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.accent),
		Thickness = 1,
		Transparency = 0.6,
	}), React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, SIZES.padding.md),
		PaddingRight = UDim.new(0, SIZES.padding.md),
	}), React.createElement("frame", {
		Size = UDim2.new(1, 0, 0.4, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromRGB(255, 255, 255),
		BackgroundTransparency = 0.9,
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}))))), React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ElementalBattleUI] Error in elemental battle UI: {err} (ID: {errorId})`)
			TS.try(function()
				ToastService.showError("Game UI Error", "The game interface encountered an error.")
			end, function(toastError)
				warn(`[ElementalBattleUI] Failed to show error toast: {toastError}`)
			end)
		end,
	}, React.createElement(ElementalBattleUI, {
		isOpen = gameUIOpen,
		onClose = function()
			return setGameUIOpen(false)
		end,
	})))) else (React.createElement("frame", {
		Size = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
	}))))
end
local root = createRoot(screenGui)
-- Render the main app with splash screen
root:render(React.createElement(MainApp))
-- All initialization is now handled by the splash screen system
