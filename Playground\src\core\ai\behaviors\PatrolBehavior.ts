import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";
import { PositionHelper } from "../../helper/PositionHelper";

export class Patrol<PERSON>ehavior implements AIBehavior {
	name = "Patrol";
	priority = 3;

	canExecute(context: AIContext): boolean {
		// Only patrol when no target is present
		return !context.target;
	}

	execute(context: AIContext): AIBehaviorResult {
		const patrolPoints = (context.blackboard.patrolPoints as Vector3[]) || [];
		const currentPatrolIndex = (context.blackboard.currentPatrolIndex as number) || 0;
		const patrolPauseTime = (context.blackboard.patrolPauseTime as number) || 0;
		const isPatrolPaused = (context.blackboard.isPatrolPaused as boolean) || false;
		const patrolPointsGenerated = (context.blackboard.patrolPointsGenerated as boolean) || false;

		// Generate patrol points if none exist and not already generated
		if (patrolPoints.size() === 0 && !patrolPointsGenerated) {
			this.generateSmartPatrolPoints(context);
			context.blackboard.patrolPointsGenerated = true;
			return { success: true, completed: false };
		}

		// Handle patrol pause at waypoints
		if (isPatrolPaused) {
			context.blackboard.patrolPauseTime = patrolPauseTime + context.deltaTime;

			// Look around while paused
			this.lookAround(context);

			if (patrolPauseTime > 2) {
				// Pause for 2 seconds at each point
				context.blackboard.isPatrolPaused = false;
				context.blackboard.patrolPauseTime = 0;
				context.blackboard.currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.size();
				print(`🚶 ${context.entityId} resuming patrol to point ${context.blackboard.currentPatrolIndex}`);
			}

			return { success: true, completed: false };
		}

		const targetPoint = patrolPoints[currentPatrolIndex];
		const distance = context.position.sub(targetPoint).Magnitude;

		if (distance <= 3) {
			// Reached patrol point - start pause
			context.blackboard.isPatrolPaused = true;
			context.blackboard.patrolPauseTime = 0;
			print(`🚶 ${context.entityId} reached patrol point ${currentPatrolIndex}, pausing`);
		} else {
			// Move towards current patrol point
			this.moveTowards(context, targetPoint);

			// Face movement direction
			PositionHelper.lookAt(context.entity, targetPoint);
		}

		// Update patrol statistics
		const totalPatrolTime = ((context.blackboard.totalPatrolTime as number) || 0) + context.deltaTime;
		context.blackboard.totalPatrolTime = totalPatrolTime;

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🚶 ${context.entityId} starting patrol`);
		context.blackboard.patrolStartTime = tick();

		// Reset patrol state
		context.blackboard.isPatrolPaused = false;
		context.blackboard.patrolPauseTime = 0;
		context.blackboard.lookDirection = 0;
		context.blackboard.patrolPointsGenerated = false; // Reset generation flag
	}

	onExit(context: AIContext): void {
		const patrolTime = tick() - ((context.blackboard.patrolStartTime as number) || tick());
		print(`🚶 ${context.entityId} ended patrol after ${math.floor(patrolTime)}s`);
	}

	private generateSmartPatrolPoints(context: AIContext): void {
		const patrolPoints: Vector3[] = [];
		const basePosition = context.position;
		const patrolRadius = (context.blackboard.patrolRadius as number) || 25;
		const numPoints = 6; // More patrol points for variety

		// Generate points in a more natural pattern
		for (let i = 0; i < numPoints; i++) {
			// Use slightly randomized angles for more natural movement
			const baseAngle = (i / numPoints) * math.pi * 2;
			const angleVariation = (math.random() - 0.5) * 0.5; // ±0.25 radian variation
			const angle = baseAngle + angleVariation;

			// Vary the radius for each point
			const radiusVariation = 0.5 + math.random() * 0.8; // 50%-130% of base radius
			const actualRadius = patrolRadius * radiusVariation;

			const x = basePosition.X + math.cos(angle) * actualRadius;
			const z = basePosition.Z + math.sin(angle) * actualRadius;

			// Try to find a valid position (basic terrain adaptation)
			const candidatePosition = new Vector3(x, basePosition.Y, z);
			const validPosition = this.findValidPatrolPosition(candidatePosition, basePosition);

			patrolPoints.push(validPosition);
		}

		// Randomize the starting point
		const startIndex = math.random(0, numPoints - 1);

		context.blackboard.patrolPoints = patrolPoints;
		context.blackboard.currentPatrolIndex = startIndex;
		context.blackboard.originalPatrolCenter = basePosition;

		print(`🚶 Generated ${numPoints} patrol points around ${basePosition}, starting at point ${startIndex}`);
	}

	private findValidPatrolPosition(candidatePosition: Vector3, fallbackPosition: Vector3): Vector3 {
		// Simple validation - in a real implementation, this would check for terrain, obstacles, etc.
		// For now, just ensure the Y coordinate stays reasonable
		const raycastResult = game
			.GetService("Workspace")
			.Raycast(candidatePosition.add(new Vector3(0, 10, 0)), new Vector3(0, -20, 0));

		if (raycastResult) {
			return new Vector3(candidatePosition.X, raycastResult.Position.Y + 3, candidatePosition.Z);
		}

		return candidatePosition; // Use original if no ground found
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				// Adjust patrol speed - slower than following or combat
				const baseSpeed = (context.blackboard.moveSpeed as number) || 16;
				humanoid.WalkSpeed = baseSpeed * 0.6; // Slower patrol speed
				humanoid.MoveTo(clearTarget);
			} else {
				this.smoothMoveTo(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothMoveTo(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private lookAround(context: AIContext): void {
		// Simulate looking around behavior while paused
		const lookDirection = (context.blackboard.lookDirection as number) || 0;
		const lookSpeed = 2; // radians per second
		const newLookDirection = lookDirection + lookSpeed * context.deltaTime;

		// Create a look target in front of the entity
		const lookDistance = 10;
		const lookTarget = context.position.add(
			new Vector3(math.cos(newLookDirection) * lookDistance, 0, math.sin(newLookDirection) * lookDistance),
		);

		PositionHelper.lookAt(context.entity, lookTarget);
		context.blackboard.lookDirection = newLookDirection;
	}

	private smoothMoveTo(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 12; // Slower patrol speed for parts
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}
}
