-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitFor<PERSON>hild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local function Slider(props)
	local isDragging, setIsDragging = React.useState(false)
	local hovered, setHovered = React.useState(false)
	local sliderRef = React.useRef()
	local _condition = props.min
	if _condition == nil then
		_condition = 0
	end
	local min = _condition
	local _condition_1 = props.max
	if _condition_1 == nil then
		_condition_1 = 1
	end
	local max = _condition_1
	local _condition_2 = props.step
	if _condition_2 == nil then
		_condition_2 = 0.01
	end
	local step = _condition_2
	local size = props.size or UDim2.new(1, 0, 0, 30)
	-- Calculate thumb position based on value
	local normalizedValue = (props.value - min) / (max - min)
	local thumbPosition = UDim2.new(normalizedValue, -8, 0.5, -8)
	local updateValue = function(inputObject)
		if not sliderRef.current or props.disabled then
			return nil
		end
		local frame = sliderRef.current
		local relativeX = inputObject.Position.X - frame.AbsolutePosition.X
		local percentage = math.clamp(relativeX / frame.AbsoluteSize.X, 0, 1)
		local newValue = min + percentage * (max - min)
		-- Apply step
		if step > 0 then
			newValue = math.round(newValue / step) * step
		end
		newValue = math.clamp(newValue, min, max)
		props.onChange(newValue)
	end
	local handleInputBegan = function(rbx, inputObject)
		if props.disabled then
			return nil
		end
		if inputObject.UserInputType == Enum.UserInputType.MouseButton1 or inputObject.UserInputType == Enum.UserInputType.Touch then
			setIsDragging(true)
			updateValue(inputObject)
		end
	end
	local handleInputChanged = function(rbx, inputObject)
		if not isDragging or props.disabled then
			return nil
		end
		if inputObject.UserInputType == Enum.UserInputType.MouseMovement or inputObject.UserInputType == Enum.UserInputType.Touch then
			updateValue(inputObject)
		end
	end
	local handleInputEnded = function(rbx, inputObject)
		if inputObject.UserInputType == Enum.UserInputType.MouseButton1 or inputObject.UserInputType == Enum.UserInputType.Touch then
			setIsDragging(false)
		end
	end
	local trackColor = if props.disabled then COLORS.border.l1 else COLORS.bg.surface
	local fillColor = if props.disabled then COLORS.border.l2 else COLORS.primary
	local thumbColor = if props.disabled then COLORS.border.l2 elseif isDragging then COLORS["primary-dark"] elseif hovered then COLORS.primary else COLORS["primary-dark"]
	local _value = props.label
	return React.createElement("frame", {
		Size = size,
		Position = props.position,
		LayoutOrder = props.layoutOrder,
		BackgroundTransparency = 1,
	}, if _value ~= "" and _value then (React.createElement("textlabel", {
		Text = props.label,
		Size = UDim2.new(1, 0, 0, 16),
		Position = UDim2.new(0, 0, 0, -20),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		TextSize = 12,
		TextXAlignment = Enum.TextXAlignment.Left,
		Font = Enum.Font.SourceSans,
	})) else nil, React.createElement("frame", {
		ref = sliderRef,
		Size = UDim2.new(1, 0, 0, 6),
		Position = UDim2.new(0, 0, 0.5, -3),
		BackgroundColor3 = Color3.fromHex(trackColor),
		BorderSizePixel = 0,
		Event = {
			InputBegan = handleInputBegan,
			InputChanged = handleInputChanged,
			InputEnded = handleInputEnded,
			MouseEnter = function()
				return not props.disabled and setHovered(true)
			end,
			MouseLeave = function()
				return setHovered(false)
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 3),
	}), React.createElement("frame", {
		Size = UDim2.new(normalizedValue, 0, 1, 0),
		BackgroundColor3 = Color3.fromHex(fillColor),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 3),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 16, 0, 16),
		Position = thumbPosition,
		BackgroundColor3 = Color3.fromHex(thumbColor),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l1),
		Thickness = 1,
		Transparency = if props.disabled then 0.5 else 0,
	}))), if props.showValue then (React.createElement("textlabel", {
		Text = tostring(math.round(props.value * 100) / 100),
		Size = UDim2.new(0, 40, 0, 16),
		Position = UDim2.new(1, 5, 0.5, -8),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(COLORS.text.secondary),
		TextSize = 12,
		TextXAlignment = Enum.TextXAlignment.Left,
		Font = Enum.Font.SourceSans,
	})) else nil)
end
return {
	Slider = Slider,
}
