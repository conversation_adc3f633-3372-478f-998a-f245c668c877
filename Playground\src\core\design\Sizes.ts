export const SIZES = {
	// Modern spacing system - larger, more generous spacing for kid-friendly design
	padding: {
		xs: 4,
		sm: 8,
		md: 16,
		lg: 24,
		xl: 32,
		"2xl": 48,
	},

	margin: {
		xs: 4,
		sm: 8,
		md: 16,
		lg: 24,
		xl: 32,
	},

	// Typography sizing - larger, more readable fonts
	fontSize: {
		xs: 10,
		sm: 12,
		md: 14, // Default size
		lg: 16,
		xl: 18,
		"2xl": 20,
		"3xl": 24,
		"4xl": 28,
	},

	// Component sizing - larger, more touch-friendly
	button: {
		width: 140, // Increased from 120 for better touch targets
		height: 44, // Increased from 40 for better accessibility
		minWidth: 100,
		padding: 16,
	},

	input: {
		width: 220, // Increased from 200
		height: 40, // Increased from 30
		padding: 12,
	},

	gridCell: {
		width: 60, // Increased from 50
		height: 60, // Increased from 50
	},

	// New component sizes for modern UI
	icon: {
		xs: 12,
		sm: 16,
		md: 20,
		lg: 24,
		xl: 32,
	},

	modal: {
		small: { width: 400, height: 300 },
		medium: { width: 600, height: 500 },
		large: { width: 800, height: 700 },
	},

	card: {
		padding: 20,
		gap: 12,
	},

	avatar: {
		sm: 32,
		md: 48,
		lg: 64,
		xl: 96,
	},

	// Responsive breakpoints for different screen sizes
	breakpoints: {
		mobile: 768,
		tablet: 1024,
		desktop: 1280,
	},
};
