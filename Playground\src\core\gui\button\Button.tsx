import * as React from "@rbxts/react";
import { COLORS, SIZES, TYP<PERSON>GRAPHY, BORDER_RADIUS } from "../../design";
import { QuickUtils } from "../../helper/GameUtils";

interface ButtonProps {
	text: string;
	onClick: () => void;
	disabled?: boolean;
	loading?: boolean;
	variant?: "primary" | "secondary" | "success" | "warning" | "error" | "ghost" | "outline";
	size?: UDim2;
	LayoutOrder?: number;
	autoSize?: boolean;
	responsive?: boolean;
}

export function Button(props: ButtonProps) {
	const [hovered, setHovered] = React.useState(false);
	const [pressed, setPressed] = React.useState(false);

	const getBackgroundColor = () => {
		if (props.disabled) return COLORS.bg.secondary;
		if (props.loading) return COLORS.bg.surface;

		const variant = props.variant ?? "primary";

		if (pressed) {
			switch (variant) {
				case "primary":
					return COLORS["primary-dark"];
				case "success":
					return COLORS["success-dark"];
				case "warning":
					return COLORS["warning-dark"];
				case "error":
					return COLORS["error-dark"];
				case "ghost":
					return COLORS.bg.hover;
				case "outline":
					return COLORS.bg.hover;
				default:
					return COLORS["primary-dark"];
			}
		}

		if (hovered) {
			switch (variant) {
				case "primary":
					return COLORS["primary-hover"];
				case "success":
					return COLORS["success-hover"];
				case "warning":
					return COLORS["warning-hover"];
				case "error":
					return COLORS["error-hover"];
				case "ghost":
					return COLORS.bg.hover;
				case "outline":
					return COLORS.bg.hover;
				default:
					return COLORS["primary-hover"];
			}
		}

		switch (variant) {
			case "primary":
				return COLORS.primary;
			case "success":
				return COLORS.success;
			case "warning":
				return COLORS.warning;
			case "error":
				return COLORS.error;
			case "ghost":
				return "transparent";
			case "outline":
				return "transparent";
			default:
				return COLORS.primary;
		}
	};

	const getTextColor = () => {
		if (props.disabled) return COLORS.text.muted;
		const variant = props.variant ?? "primary";

		switch (variant) {
			case "ghost":
			case "outline":
				return hovered ? COLORS.primary : COLORS.text.main;
			default:
				return COLORS.text.inverse;
		}
	};

	const getBorderColor = () => {
		if (props.disabled) return COLORS.border.l2;
		const variant = props.variant ?? "primary";

		if (variant === "outline") {
			return hovered ? COLORS.primary : COLORS.border.l3;
		}
		return "transparent";
	};

	const getBackgroundTransparency = () => {
		const variant = props.variant ?? "primary";
		if (variant === "ghost") return 1;
		if (variant === "outline") return hovered ? 0.95 : 1;
		return 0;
	};

	const size = props.size ?? new UDim2(0, 140, 0, 44);

	return (
		<frame
			Size={size}
			Position={new UDim2(0, 0, 0, 0)}
			LayoutOrder={props.LayoutOrder}
			BackgroundTransparency={1}
			BorderSizePixel={0}
		>
			{/* Modern drop shadow with improved depth */}
			<frame
				Size={new UDim2(1, 4, 1, 4)}
				Position={new UDim2(0, 2, 0, 2)}
				BackgroundColor3={Color3.fromRGB(0, 0, 0)}
				BackgroundTransparency={0.85}
				BorderSizePixel={0}
				ZIndex={-1}
			>
				<uicorner CornerRadius={new UDim(0, 10)} />
			</frame>

			<textbutton
				Text={props.text}
				TextColor3={QuickUtils.safeColor(getTextColor(), COLORS.text.main)}
				BackgroundColor3={QuickUtils.safeColor(getBackgroundColor(), COLORS.bg.base)}
				BackgroundTransparency={getBackgroundTransparency()}
				Size={new UDim2(1, 0, 1, 0)}
				Position={new UDim2(0, 0, 0, 0)}
				Font={Enum.Font.GothamBold}
				TextSize={14}
				AutoButtonColor={false}
				BorderSizePixel={0}
				Event={{
					Activated: () => {
						if (!props.disabled && !props.loading) {
							props.onClick();
						}
					},
					MouseEnter: () => {
						if (!props.disabled && !props.loading) {
							setHovered(true);
						}
					},
					MouseLeave: () => {
						setHovered(false);
						setPressed(false);
					},
					MouseButton1Down: () => {
						if (!props.disabled && !props.loading) {
							setPressed(true);
						}
					},
					MouseButton1Up: () => {
						setPressed(false);
					},
				}}
			>
				<uicorner CornerRadius={new UDim(0, 8)} />

				{/* Enhanced border stroke for outline and focus states */}
				<uistroke
					Color={QuickUtils.safeColor(getBorderColor(), COLORS.border.base)}
					Thickness={props.variant === "outline" ? 2 : 0}
					Transparency={props.variant === "outline" ? 0 : 1}
				/>

				{/* Subtle gradient overlay for depth */}
				<frame
					Size={new UDim2(1, 0, 0.5, 0)}
					Position={new UDim2(0, 0, 0, 0)}
					BackgroundColor3={Color3.fromRGB(255, 255, 255)}
					BackgroundTransparency={0.9}
					BorderSizePixel={0}
					ZIndex={1}
				>
					<uicorner CornerRadius={new UDim(0, 8)} />
				</frame>
			</textbutton>
		</frame>
	);
}
