export enum QuestType {
	Main = "Main",
	Side = "Side",
	Daily = "Daily",
	Weekly = "Weekly",
	Achievement = "Achievement",
	Tutorial = "Tutorial",
	Event = "Event",
}

export enum QuestStatus {
	NotStarted = "NotStarted",
	Available = "Available",
	Active = "Active",
	Completed = "Completed",
	Failed = "Failed",
	Abandoned = "Abandoned",
	Expired = "Expired",
}

export enum ObjectiveType {
	KillTarget = "KillTarget",
	CollectItem = "CollectItem",
	ReachLocation = "ReachLocation",
	TalkToNPC = "TalkToNPC",
	UseItem = "UseItem",
	Craft = "Craft",
	Timer = "Timer",
	Custom = "Custom",
}

export interface QuestReward {
	type: "item" | "currency" | "experience" | "unlock";
	itemId?: string;
	quantity?: number;
	currencyType?: string;
	amount?: number;
	experiencePoints?: number;
	unlockId?: string;
}

export interface QuestObjective {
	id: string;
	type: ObjectiveType;
	description: string;
	targetId?: string; // Entity ID, Item ID, Location ID, etc.
	targetCount: number;
	currentCount: number;
	isOptional: boolean;
	isCompleted: boolean;
	isHidden: boolean; // Don't show to player until revealed
	requirements?: {
		prerequisites?: string[]; // Other objective IDs that must be completed first
		items?: Record<string, number>;
		level?: number;
	};
}

export interface QuestDefinition {
	id: string;
	name: string;
	description: string;
	type: QuestType;
	category: string;
	level: number; // Recommended player level
	maxLevel?: number; // Maximum level to receive quest
	isRepeatable: boolean;
	cooldownHours?: number; // For repeatable quests
	expirationHours?: number; // Quest expires after this time
	prerequisites: string[]; // Quest IDs that must be completed first
	objectives: QuestObjective[];
	rewards: QuestReward[];
	bonusRewards?: QuestReward[]; // Additional rewards for completing all optional objectives
	autoStart: boolean; // Automatically start when prerequisites are met
	autoComplete: boolean; // Automatically complete when all objectives are done
	tags: string[];
	giver?: string; // NPC ID that gives this quest
	location?: Vector3; // Where the quest is available
	iconId?: string;
	soundId?: string;
}

export interface QuestProgress {
	questId: string;
	status: QuestStatus;
	startTime?: number;
	completionTime?: number;
	expirationTime?: number;
	objectives: Map<string, QuestObjective>;
	timesCompleted: number; // For repeatable quests
	lastCompletionTime?: number;
}

export interface QuestEvent {
	type: "started" | "completed" | "failed" | "abandoned" | "objective_completed" | "expired";
	questId: string;
	objectiveId?: string;
	timestamp: number;
	playerId?: string;
}

export interface QuestConfiguration {
	enableAutoProgress: boolean;
	enableNotifications: boolean;
	maxActiveQuests: number;
	enableQuestLog: boolean;
	enableQuestTracking: boolean;
	saveProgress: boolean;
}
